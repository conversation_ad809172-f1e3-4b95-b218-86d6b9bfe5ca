"""
Corrected Excel Portfolio Framework
==================================

Framework that properly reads the Category column from AUM and Vintage sheets
to create portfolio combinations with naming format 'sheetname_category_portfolio'.

Author: Augment Agent
Date: 2025-06-29
"""

import pandas as pd
import numpy as np
from datetime import datetime
from sip_portfolio_analyzer import SIPPortfolioAnalyzer

def load_nav_data_from_excel(excel_file_path: str) -> pd.DataFrame:
    """
    Load NAV data from Excel file using the exact method.
    """
    print("📊 Loading NAV data from Excel file...")
    
    # Load AUM sheet
    aum_sheet = pd.read_excel(excel_file_path, sheet_name='AUM')
    aum_sheet = aum_sheet.iloc[33:, 1:]
    aum_sheet.columns = aum_sheet.iloc[1]
    aum_sheet = aum_sheet.iloc[2:, :]
    aum_sheet = aum_sheet.dropna(how='all', axis=1)
    aum_sheet = aum_sheet.dropna(how='all', axis=0)
    aum_sheet.index = aum_sheet.iloc[:, 0]
    aum_sheet = aum_sheet.iloc[:, 1:]
    aum_sheet = aum_sheet.apply(pd.to_numeric, errors='coerce')
    aum_sheet.index.name = 'Date'
    aum_sheet.index = pd.to_datetime(aum_sheet.index)
    
    # Load Vintage sheet
    vintage_sheet = pd.read_excel(excel_file_path, sheet_name='Vintage')
    vintage_sheet = vintage_sheet.iloc[32:, 1:]
    vintage_sheet.columns = vintage_sheet.iloc[1]
    vintage_sheet = vintage_sheet.iloc[2:, :]
    vintage_sheet = vintage_sheet.dropna(how='all', axis=1)
    vintage_sheet = vintage_sheet.dropna(how='all', axis=0)
    vintage_sheet.index = vintage_sheet.iloc[:, 0]
    vintage_sheet = vintage_sheet.iloc[:, 1:]
    vintage_sheet = vintage_sheet.apply(pd.to_numeric, errors='coerce')
    vintage_sheet.index.name = 'Date'
    vintage_sheet.index = pd.to_datetime(vintage_sheet.index)
    
    # Merge the datasets
    df = aum_sheet.merge(vintage_sheet, left_index=True, right_index=True, how='outer', suffixes=('', '_drop')).ffill()
    # Drop duplicate columns
    df = df.loc[:, ~df.columns.str.endswith('_drop')]
    
    print(f"✅ Loaded NAV data: {df.shape}")
    print(f"📅 Date range: {df.index.min().strftime('%Y-%m-%d')} to {df.index.max().strftime('%Y-%m-%d')}")
    print(f"💼 Available funds: {len(df.columns)}")
    
    return df

def read_fund_categories_from_excel(excel_file_path: str) -> tuple:
    """
    Read fund categories from the Category column in AUM and Vintage sheets.
    """
    print("📋 Reading fund categories from Excel sheets...")
    
    try:
        # Read AUM sheet metadata - try different header positions
        print("  📊 Reading AUM sheet categories...")
        aum_meta = None
        
        # Try common header positions
        for header_row in [0, 1, 2, 3]:
            try:
                temp_df = pd.read_excel(excel_file_path, sheet_name='AUM', header=header_row)
                if 'Scheme Name' in temp_df.columns and 'Category' in temp_df.columns:
                    aum_meta = temp_df
                    print(f"    ✅ Found AUM metadata with header at row {header_row}")
                    break
            except:
                continue
        
        if aum_meta is None:
            print("    ⚠️ Could not find AUM metadata with standard headers")
            aum_meta = pd.DataFrame()
        else:
            # Clean the data
            aum_meta = aum_meta.dropna(subset=['Scheme Name'])
            aum_meta = aum_meta[aum_meta['Scheme Name'] != 'Scheme Name']  # Remove header duplicates
            print(f"    📊 AUM funds found: {len(aum_meta)}")
            print(f"    📊 AUM categories: {aum_meta['Category'].dropna().unique()}")
        
        # Read Vintage sheet metadata
        print("  📈 Reading Vintage sheet categories...")
        vintage_meta = None
        
        # Try common header positions
        for header_row in [0, 1, 2, 3]:
            try:
                temp_df = pd.read_excel(excel_file_path, sheet_name='Vintage', header=header_row)
                if 'Scheme Name' in temp_df.columns and 'Category' in temp_df.columns:
                    vintage_meta = temp_df
                    print(f"    ✅ Found Vintage metadata with header at row {header_row}")
                    break
            except:
                continue
        
        if vintage_meta is None:
            print("    ⚠️ Could not find Vintage metadata with standard headers")
            vintage_meta = pd.DataFrame()
        else:
            # Clean the data
            vintage_meta = vintage_meta.dropna(subset=['Scheme Name'])
            vintage_meta = vintage_meta[vintage_meta['Scheme Name'] != 'Scheme Name']  # Remove header duplicates
            print(f"    📈 Vintage funds found: {len(vintage_meta)}")
            print(f"    📈 Vintage categories: {vintage_meta['Category'].dropna().unique()}")
        
        return aum_meta, vintage_meta
        
    except Exception as e:
        print(f"❌ Error reading categories: {str(e)}")
        return pd.DataFrame(), pd.DataFrame()

def create_category_based_portfolios(nav_data: pd.DataFrame, aum_meta: pd.DataFrame, vintage_meta: pd.DataFrame) -> dict:
    """
    Create portfolios based on the actual Category column from Excel sheets.
    """
    print("🎯 Creating category-based portfolios from Excel data...")
    
    portfolios_dict = {}
    available_funds = set(nav_data.columns)
    
    # Process AUM sheet categories
    if not aum_meta.empty and 'Category' in aum_meta.columns:
        print("\n📊 Processing AUM categories:")
        aum_categories = aum_meta.groupby('Category')['Scheme Name'].apply(list).to_dict()
        
        for category, funds in aum_categories.items():
            if pd.notna(category) and category != '':
                # Filter funds that are available in NAV data
                available_category_funds = [fund for fund in funds if fund in available_funds]
                
                if len(available_category_funds) >= 3:  # Minimum 3 funds per portfolio
                    # Clean category name for portfolio naming
                    clean_category = category.replace(' ', '_').replace('&', 'and').replace('/', '_').replace('-', '_')
                    portfolio_name = f"AUM_{clean_category}_portfolio"
                    portfolios_dict[portfolio_name] = available_category_funds
                    print(f"  ✅ {portfolio_name}: {len(available_category_funds)} funds")
                    print(f"      Funds: {', '.join(available_category_funds[:3])}{'...' if len(available_category_funds) > 3 else ''}")
                else:
                    print(f"  ⚠️ {category}: Only {len(available_category_funds)} available funds (minimum 3 required)")
    else:
        print("⚠️ No AUM category data found")
    
    # Process Vintage sheet categories
    if not vintage_meta.empty and 'Category' in vintage_meta.columns:
        print("\n📈 Processing Vintage categories:")
        vintage_categories = vintage_meta.groupby('Category')['Scheme Name'].apply(list).to_dict()
        
        for category, funds in vintage_categories.items():
            if pd.notna(category) and category != '':
                # Filter funds that are available in NAV data
                available_category_funds = [fund for fund in funds if fund in available_funds]
                
                if len(available_category_funds) >= 3:  # Minimum 3 funds per portfolio
                    # Clean category name for portfolio naming
                    clean_category = category.replace(' ', '_').replace('&', 'and').replace('/', '_').replace('-', '_')
                    portfolio_name = f"Vintage_{clean_category}_portfolio"
                    portfolios_dict[portfolio_name] = available_category_funds
                    print(f"  ✅ {portfolio_name}: {len(available_category_funds)} funds")
                    print(f"      Funds: {', '.join(available_category_funds[:3])}{'...' if len(available_category_funds) > 3 else ''}")
                else:
                    print(f"  ⚠️ {category}: Only {len(available_category_funds)} available funds (minimum 3 required)")
    else:
        print("⚠️ No Vintage category data found")
    
    print(f"\n🎉 Created {len(portfolios_dict)} portfolios total")
    return portfolios_dict

def run_corrected_excel_analysis(excel_file_path: str = 'MF_NAV/Scheme Names - Traditional SIP.xlsx',
                                sip_amount: float = 10000,
                                sip_start_date: str = "2018-01-01",
                                sip_end_date: str = "2024-06-30") -> pd.DataFrame:
    """
    Complete analysis pipeline using correct Excel category reading.
    """
    print("🚀 Corrected Excel Portfolio Analysis")
    print("Using explicit Category columns from AUM and Vintage sheets")
    print("=" * 70)
    
    # Step 1: Load NAV data
    try:
        nav_data = load_nav_data_from_excel(excel_file_path)
    except Exception as e:
        print(f"❌ Error loading NAV data: {str(e)}")
        return pd.DataFrame()
    
    # Step 2: Read fund categories from Excel
    try:
        aum_meta, vintage_meta = read_fund_categories_from_excel(excel_file_path)
        if aum_meta.empty and vintage_meta.empty:
            print("❌ No category metadata found in Excel sheets")
            return pd.DataFrame()
    except Exception as e:
        print(f"❌ Error reading categories: {str(e)}")
        return pd.DataFrame()
    
    # Step 3: Create category-based portfolios
    try:
        portfolios_dict = create_category_based_portfolios(nav_data, aum_meta, vintage_meta)
        if not portfolios_dict:
            print("❌ No portfolios created from Excel categories")
            return pd.DataFrame()
    except Exception as e:
        print(f"❌ Error creating portfolios: {str(e)}")
        return pd.DataFrame()
    
    # Step 4: Run analysis
    try:
        analyzer = SIPPortfolioAnalyzer(nav_data)
        
        print(f"\n📊 Running comprehensive analysis...")
        print(f"💰 SIP Amount: ₹{sip_amount:,} per month")
        print(f"📅 SIP Period: {sip_start_date} to {sip_end_date}")
        
        results = analyzer.analyze_portfolios(
            portfolios_dict=portfolios_dict,
            sip_amount=sip_amount,
            sip_start_date=sip_start_date,
            sip_end_date=sip_end_date
        )
        
        if results.empty:
            print("❌ Analysis failed")
            return pd.DataFrame()
            
    except Exception as e:
        print(f"❌ Error in analysis: {str(e)}")
        return pd.DataFrame()
    
    # Step 5: Display and export results
    print(f"\n📈 Analysis Results:")
    print(f"✅ Successfully analyzed {len(results)} portfolios")
    
    # Show key results
    key_columns = ['Portfolio Name', 'Number of Funds', 'XIRR (%)', 'CAGR (%)', 'Sharpe Ratio']
    print(f"\n📊 Key Results:")
    print(results[key_columns].to_string(index=False))
    
    # Display top performers
    analyzer.display_top_performers(results, top_n=5)
    
    # Category comparison
    print(f"\n🏆 AUM vs Vintage Category Comparison:")
    aum_results = results[results['Portfolio Name'].str.contains('AUM_')]
    vintage_results = results[results['Portfolio Name'].str.contains('Vintage_')]
    
    if not aum_results.empty and not vintage_results.empty:
        aum_avg_xirr = aum_results['XIRR (%)'].mean()
        vintage_avg_xirr = vintage_results['XIRR (%)'].mean()
        
        print(f"  📊 Average AUM Portfolio XIRR: {aum_avg_xirr:.2f}%")
        print(f"  📈 Average Vintage Portfolio XIRR: {vintage_avg_xirr:.2f}%")
        print(f"  🥇 Better Strategy: {'AUM' if aum_avg_xirr > vintage_avg_xirr else 'Vintage'}")
    
    # Export results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    excel_filename = f"corrected_excel_portfolio_analysis_{timestamp}.xlsx"
    analyzer.export_comparison_table(results, excel_filename)
    
    # Generate plots
    analyzer.plot_comparison(results, metric='XIRR (%)', top_n=min(15, len(results)), save_plot=True)
    
    print(f"\n🎉 Analysis Complete!")
    print(f"📁 Results exported to: {excel_filename}")
    
    return results

if __name__ == "__main__":
    print("🎊 Corrected Excel Portfolio Framework")
    print("=" * 70)
    
    # Run the corrected analysis
    results = run_corrected_excel_analysis()
    
    if not results.empty:
        print("\n" + "=" * 70)
        print("📚 Analysis Summary:")
        print("✅ Loaded NAV data directly from Excel file")
        print("✅ Read explicit Category columns from AUM and Vintage sheets")
        print("✅ Created portfolios with naming format: 'sheetname_category_portfolio'")
        print("✅ Used actual fund categorizations from Excel")
        print("✅ Analyzed all portfolio combinations")
        print("✅ Generated comprehensive comparison table")
        print("✅ Calculated XIRR for equal-weighted SIP portfolios")
        print("✅ Calculated all non-SIP performance metrics")
        print("✅ Exported results and generated plots")
        print("=" * 70)
    else:
        print("\n❌ Analysis could not be completed")
        print("🔧 Please ensure Excel file is not open and try again")
