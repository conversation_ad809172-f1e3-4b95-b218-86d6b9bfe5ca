import pandas as pd
from dateutil.relativedelta import relativedelta
import numpy as np
import re
import matplotlib.pyplot as plt
from matplotlib import pyplot as plt
from matplotlib.pyplot import figure
import warnings
warnings.filterwarnings("ignore")
import math
import os
from datetime import date, timedelta, datetime
import time
from tqdm import tqdm
import pyodbc
import seaborn as sns
from scipy import stats
import xlsxwriter
from matplotlib.ticker import MaxNLocator
from matplotlib.backends.backend_pdf import PdfPages
import itertools
from data import Data
d = Data()
import numpy_financial as npf
from scipy.optimize import newton, brentq
start_time = time.perf_counter()

madp_master_data = pd.read_excel(r"\\*************\c$\Users\Administrator\PycharmProjects\Projects\MADP-ALPHA\madp_data_bb_things\MADP-Master-Data.xlsx")
india_gold = madp_master_data[['Date', 'Gold per ounce in INR']]
gmdp = pd.read_excel(r'\\*************\c$\Users\Administrator\PycharmProjects\Projects\globalMacroDataPipeline\Historic Macro Data2.xlsx')

# All Fund Indices Data
df = pd.read_excel('./input_data/Fund Indices Data.xlsx').dropna()
df.Date = pd.to_datetime(df.Date)
df = df.sort_index(ascending=True)
df = df.iloc[1:,:]

df = pd.merge(df, india_gold, on='Date', how='left').merge(gmdp[['Date', 'India 10Y']], on='Date', how='left')
df.index = pd.to_datetime(df.index)
df.set_index('Date', inplace=True)
df = df.dropna()
print(df.shape)
df.tail() 


conservative_folio = {
    'Nifty LargeMidCap 250':20.0, 
    'Nifty 100 Quality 30':20.0,
    'Nifty 500 Equal Weight':20.0,
    'Nifty Alpha Low Vol 30':20.0,
    'Nifty 500':20.0,
   
}

moderate_folio = {
    'Nifty 500 Value 50':20.0,
    'Nifty MidSmallcap400 Momentum Quality 100':20.0,
    "Nifty 200 Quality 30":20.0,
    "Nifty Alpha Low Vol 30":20.0,
    "Nifty Smallcap 250":20.0,
   
}


aggresive_folio = {
    'Nifty 500 Momentum 50':20.0,
    'Nifty Microcap 250':20.0,
    'Nifty Midcap 150':20.0,
    "Nifty MidSmallcap400 Momentum Quality 100":20.0,
    "Nifty 500":20.0,
    
}

# ------------------------------------

multiasset_conservative_folio = {
    'Nifty LargeMidCap 250':10.0, 
    'Nifty 100 Quality 30':10.0,
    'Nifty 500 Equal Weight':10.0,
    'Nifty Alpha Low Vol 30':10.0,
    'Nifty 500':10.0,
    'GOLD(Ounce/INR)':25.0,
    'India 10Y':25.0,
}

multiasset_moderate_folio = {
    'Nifty 500 Value 50':13.0,
    'Nifty MidSmallcap400 Momentum Quality 100':13.0,
    "Nifty 200 Quality 30":13.0,
    "Nifty Alpha Low Vol 30":13.0,
    "Nifty Smallcap 250":13.0,
    'GOLD(Ounce/INR)':17.5,
    'India 10Y':17.5,
}


multiasset_aggresive_folio = {
    'Nifty 500 Momentum 50':16.0,
    'Nifty Microcap 250':16.0,
    'Nifty Midcap 150':16.0,
    "Nifty MidSmallcap400 Momentum Quality 100":16.0,
    "Nifty 500":16.0,
    'GOLD(Ounce/INR)':10.0,
    'India 10Y':10.0,
}

bm1 = {
    'Nifty 50': 100.0
}

bm2 = {
    'Nifty 500': 100.0
}

# ----------------------------------------------
# investor_style_folio = multiasset_conservative_folio    
# investor_style_folio = multiasset_moderate_folio    
investor_style_folio = bm2   
# ----------------------------------------------

####################################################
# Normalize weights to ensure they sum to 100%
####################################################

portfolio_weights = {k: v / 100 for k, v in investor_style_folio.items()}
component_navs = df.loc[:, investor_style_folio.keys()].copy()

sip_dates = pd.date_range(start=component_navs.index[0], end=component_navs.index[-1], freq='MS') + pd.Timedelta(days=1)

def validate_sip_dates(sip_dates, dataFrame):
    rebalance_dates_adjusted = []
    for date in sip_dates:
        # Find the first valid date on or after the rebalance date
        valid_date = dataFrame.index[dataFrame.index >= date][0]
        rebalance_dates_adjusted.append(valid_date)
        
    if dataFrame.index[0] not in rebalance_dates_adjusted:
        rebalance_dates_adjusted.insert(0, dataFrame.index[0])
    
    print("## List of SIP dates generated. ##")
    return rebalance_dates_adjusted

sip_dates = validate_sip_dates(sip_dates, component_navs)

units_df = pd.DataFrame(0, index=component_navs.index, columns=component_navs.columns)

initial_corpus = 100000
sip_amount = initial_corpus

# Loop through all dates in the NAV data
for i, date in enumerate(tqdm(component_navs.index, desc="Calculating NAV")):
    if date in sip_dates:
        # Calculate units purchased on SIP dates
        sip_units = {fund: (sip_amount * weight) / component_navs.loc[date, fund] 
                     for fund, weight in portfolio_weights.items()}
        # Add new units to cumulative units
        if i > 0:  # Ensure there's a previous day's units
            units_df.loc[date] = units_df.iloc[i - 1] + pd.Series(sip_units)
        else:  # First row, initialize with SIP units
            units_df.loc[date] = pd.Series(sip_units)
    else:
        # For non-SIP dates, carry forward the previous day's units
        if i > 0:  # Ensure there's a previous day
            units_df.loc[date] = units_df.iloc[i - 1]


sip_portfolio_value = (units_df * component_navs).sum(axis=1)
sip_portfolio_value = sip_portfolio_value.to_frame(name='Total Portfolio Value')

# Step 1: Create cash flows
cash_flows = []

# Add SIP amounts as negative cash flows
for date in sip_dates:
    cash_flows.append({'date': date, 'value': -sip_amount})

# Add the final portfolio value as a positive cash flow
final_value = sip_portfolio_value.squeeze().iloc[-1]
cash_flows.append({'date': sip_portfolio_value.squeeze().index[-1], 'value': final_value})

# Convert to DataFrame for easier manipulation
cash_flow_df = pd.DataFrame(cash_flows)

# Step 2: Calculate XIRR function
def xirr(cash_flows, guess=0.1):
    dates = pd.to_datetime(cash_flows['date'])
    values = cash_flows['value']
    years = (dates - dates.iloc[0]).dt.days / 365

    def xirr_equation(rate):
        return np.sum(values / (1 + rate) ** years)

    try:
        return newton(xirr_equation, guess)
    except (RuntimeError, OverflowError):
        return np.nan 

# Step 3: Compute XIRR
calculated_xirr = xirr(cash_flow_df)
print(f"XIRR: {calculated_xirr:.2%}")

# Calculate initial units using the initial corpus
initial_units = {fund: (initial_corpus * weight) / component_navs.iloc[0][fund] 
                 for fund, weight in portfolio_weights.items()}

# Convert to a DataFrame with constant units for all dates
pseudo_units_df = pd.DataFrame(initial_units, index=component_navs.index)

# Calculate pseudo-portfolio value
non_sip_portfolio_value = (pseudo_units_df * component_navs).sum(axis=1)

# Convert to a DataFrame for easier handling
non_sip_portfolio_value = non_sip_portfolio_value.to_frame(name='Non-SIP Portfolio Value')

non_sip_portfolio_value['dailyPctChange'] = non_sip_portfolio_value['Non-SIP Portfolio Value'].pct_change()

def validate_quarter_end_dates(sip_dates, dataFrame):
    rebalance_dates_adjusted = []
    for date in sip_dates:
        # Find the first valid date on or after the rebalance date
        valid_date = dataFrame.index[dataFrame.index <= date][-1]
        rebalance_dates_adjusted.append(valid_date)
    print("## List of Quarter-End dates generated. ##")
    return rebalance_dates_adjusted

# Tracking error pct
annual_tracking_error = 0.015

rbdate_counter = 0
for date in validate_quarter_end_dates(non_sip_portfolio_value.resample('QE').last().index, component_navs)[:-1]:
    rbdate_counter +=1
    non_sip_portfolio_value.loc[date, 'dailyPctChange'] -= annual_tracking_error/4

non_sip_portfolio_value['Non-SIP Portfolio Value (After Tracking Error)'] = (1 + non_sip_portfolio_value['dailyPctChange']).cumprod()
non_sip_portfolio_value['Non-SIP Portfolio Value (After Tracking Error)'] = non_sip_portfolio_value['Non-SIP Portfolio Value (After Tracking Error)']/non_sip_portfolio_value['Non-SIP Portfolio Value (After Tracking Error)'].dropna().iloc[0] * initial_corpus
# non_sip_portfolio_value.dropna(inplace=True)

# roll_3y_cagr = (df['Nifty 500']
#                 .pct_change(252*3)
#                 .add(1)
#                 .pow(1/3)
#                 .sub(1)
#                 .mul(100))

# roll_5y_cagr = (df['Nifty 500']
#                 .pct_change(252*5)
#                 .add(1)
#                 .pow(1/5)
#                 .sub(1)
#                 .mul(100))

# roll_3y_cagr.to_excel('./msukaanPPTCharts/Nifty 500 -3Y Rolling CAGR.xlsx')
# roll_5y_cagr.to_excel('./msukaanPPTCharts/Nifty 500 -5Y Rolling CAGR.xlsx')
# print('## Saved Charts ##')

class FinancialMetrics:
    """
    A comprehensive class for analyzing drawdowns and calculating key performance metrics.
    """
    
    def __init__(self, nav_df):
        """
        Initialize the FinancialMetrics class with NAV data.
        
        Parameters:
        nav_df (pd.DataFrame or pd.Series): DataFrame or Series containing NAV data.
        """
        self.nav_series = nav_df.squeeze()  
        self.metrics = {}
        
        # Handle insufficient data
        if len(self.nav_series) < 252:
            self.metrics = {metric: np.nan for metric in [
                "CAGR", "MDD", "Avg_Top_5_DD", "Volatility", "Sharpe", 
                "Sortino", "Rolling_1Y_Mean_Return", "Rolling_3Y_Mean_Return",
                "Rolling_3Y_Volatility", "Closing NAV"]}
            return
        
        # Drop NaN values for reliable calculations
        self.nav_series = self.nav_series.dropna()
    
    # ========================= Key Performance Metrics =========================
    def calculate_cagr(self):
        """Calculate Compound Annual Growth Rate (CAGR)."""
        start_nav = self.nav_series.iloc[0]
        end_nav = self.nav_series.iloc[-1]
        num_years = (len(self.nav_series)) / 252
        self.metrics["CAGR"] = (end_nav / start_nav) ** (1 / num_years) - 1 if num_years > 0 else np.nan
    
    def calculate_sharpe_ratio(self, risk_free_rate=0.0):
        """Calculate Sharpe Ratio."""
        returns = self.nav_series.pct_change().dropna()
        annual_return = returns.mean() * 252
        annual_volatility = returns.std() * np.sqrt(252)
        self.metrics["Sharpe"] = (annual_return - risk_free_rate) / annual_volatility if annual_volatility > 0 else np.nan
    
    def calculate_sortino_ratio(self, risk_free_rate=0.0):
        """Calculate Sortino Ratio."""
        returns = self.nav_series.pct_change().dropna()
        annual_return = returns.mean() * 252
        downside_returns = returns[returns < 0]
        downside_deviation = downside_returns.std() * np.sqrt(252)
        self.metrics["Sortino"] = (annual_return - risk_free_rate) / downside_deviation if downside_deviation > 0 else np.nan

    def calculate_rolling_metrics(self, window=252):
        """Calculate rolling metrics."""
        # Rolling 1-year mean return
        daily_return_series = self.nav_series.pct_change().dropna()
        return_series = self.nav_series.pct_change(window).dropna() 
        self.metrics["Rolling_1Y_Mean_Return"] = return_series.mean()
    
        # Rolling 1-year volatility
        rolling_volatility = daily_return_series.rolling(window).std() * np.sqrt(window)
        self.metrics["Rolling_1Y_Volatility"] = rolling_volatility.iloc[-1] if not rolling_volatility.empty else np.nan
        
        # Rolling 3-year mean return
        return_series = self.nav_series.pct_change(window*3).dropna()
        self.metrics["Rolling_3Y_Mean_Return"] = return_series.mean()

        # Rolling 5-year mean return
        return_series = self.nav_series.pct_change(window*5).dropna() 
        self.metrics["Rolling_5Y_Mean_Return"] = return_series.mean()

        # Rolling 3-year volatility
        rolling_volatility = daily_return_series.rolling(window*3).std() * np.sqrt(window)
        self.metrics["Rolling_3Y_Volatility"] = rolling_volatility.iloc[-1]  if not rolling_volatility.empty else np.nan


    def calculate_drawdowns(self):
        """Calculate Maximum Drawdown (MDD) and Mean of Top 5 Drawdowns."""
        def to_drawdown_series(prices):
            return prices / np.maximum.accumulate(prices) - 1.0

        def drawdown_details(drawdown):
            starts, ends = [], []
            for i, value in enumerate(drawdown):
                if i > 0 and value == 0 and drawdown[i - 1] < 0:
                    ends.append(i - 1)
                if value < 0 and (i == 0 or drawdown[i - 1] == 0):
                    starts.append(i)
            if len(starts) > len(ends):
                ends.append(len(drawdown) - 1)
            drawdown_data = []
            for start, end in zip(starts, ends):
                drawdown_data.append((start, drawdown[start:end + 1].idxmin(), end, drawdown[start:end + 1].min()))
            return pd.DataFrame(drawdown_data, columns=['Start', 'Valley', 'End', 'Drawdown'])

        nav_series = self.nav_series
        drawdown = to_drawdown_series(nav_series)
        details = drawdown_details(drawdown)
        if not details.empty:
            self.metrics["MDD"] = details['Drawdown'].min() 
            self.metrics["Avg_Top_5_DD"] = details.nsmallest(5, 'Drawdown')['Drawdown'].mean() 
            self.metrics["Avg_Top_10_DD"] = details.nsmallest(10, 'Drawdown')['Drawdown'].mean() 
        else:
            self.metrics["MDD"] = np.nan
            self.metrics["Avg_Top_5_DD"] = np.nan

    def generate_metrics(self):
        """Calculate all key performance metrics."""
        self.calculate_cagr()
        self.calculate_drawdowns()
        self.calculate_sharpe_ratio()
        self.calculate_sortino_ratio()
        self.calculate_rolling_metrics()
        return self.metrics


fm = FinancialMetrics(df[['Nifty 50']])

# print()
print("-------------------------")
print(f"XIRR: {calculated_xirr:.4%}")
pd.DataFrame([fm.generate_metrics()])


