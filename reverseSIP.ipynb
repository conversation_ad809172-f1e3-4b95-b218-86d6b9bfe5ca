import pandas as pd
import numpy as np
import itertools
from multiprocessing import cpu_count
from scipy.optimize import newton, brentq
from joblib import Parallel, delayed
import matplotlib.pyplot as plt
%matplotlib inline
import time
from datetime import date, datetime
from tqdm import tqdm


class SIPAllocator:
    def __init__(self, component_navs: pd.DataFrame, sip_dates: list, monthly_investment: float):
        """
        Initialize the SIPAllocator.

        Parameters:
        - component_navs (pd.DataFrame): NAV data with datetime index and columns as asset names.
        - sip_dates (list): List of datetime SIP dates.
        - monthly_investment (float): Amount invested each SIP.
        """
        self.navs = component_navs
        self.sip_dates = pd.to_datetime(sip_dates)
        self.monthly_investment = monthly_investment
        num_assets = len(self.navs.columns)
        self.weights = {col: 1 / num_assets for col in self.navs.columns}
        self.units_df = pd.DataFrame(0.0, index=self.navs.index, columns=self.navs.columns)

    def allocate_units(self):
        """
        Allocate SIP units across all dates based on NAVs and SIP schedule using vectorized operations.

        Returns:
        - pd.DataFrame: DataFrame of cumulative units held in each asset over time.
        """
        # Filter NAV rows for SIP dates
        sip_navs = self.navs.loc[self.navs.index.isin(self.sip_dates)].copy()

        # Convert weights to a NumPy array (aligned to column order)
        weight_array = np.array([self.weights[col] for col in self.navs.columns])

        # Calculate the units purchased per SIP date
        allocated_units = (self.monthly_investment * weight_array) / sip_navs.values

        # Create a DataFrame of units bought on SIP dates
        sip_units_df = pd.DataFrame(allocated_units, index=sip_navs.index, columns=self.navs.columns)

        # Reindex to full NAV date range, filling with zeros on non-SIP dates
        sip_units_df = sip_units_df.reindex(self.navs.index, fill_value=0.0)

        # Cumulative sum of units to get total holdings at each date
        self.units_df = sip_units_df.cumsum()
        return self.units_df


class XIRRCalculator:
    def __init__(self, cash_flows):
        self.cash_flows = pd.DataFrame(cash_flows)
        self.cash_flows['date'] = pd.to_datetime(self.cash_flows['date'])
        self.cash_flows.sort_values(by='date', inplace=True)

    def _xirr_equation(self, rate):
        dates = self.cash_flows['date']
        values = self.cash_flows['value']
        years = (dates - dates.iloc[0]).dt.days / 365
        return np.sum(values / (1 + rate) ** years)

    def compute(self, guess=0.1):
        try:
            # First try Newton method for speed
            return newton(self._xirr_equation, guess)
        except Exception:
            try:
                # Brent's method is more stable
                return brentq(self._xirr_equation, -0.9999, 10)
            except Exception as e:
                print(f"XIRR failed with error: {e}")
                return np.nan


class FinancialMetrics:
    def __init__(self, nav_df, sip_amount=None, sip_dates=None):
        self.nav_series = nav_df.squeeze().dropna()
        self.sip_amount = sip_amount
        self.sip_dates = pd.to_datetime(sip_dates) if sip_dates is not None else None
        self.metrics = {}

        if len(self.nav_series) < 252:
            self.metrics = {k: np.nan for k in [
                "CAGR", "MDD", "Avg_Top_5_DD", "Volatility", "Sharpe",
                "Sortino", "Rolling_1Y_Mean_CAGR", "Rolling_3Y_Mean_CAGR",
                "Rolling_1Y_Median_CAGR", "Rolling_3Y_Median_CAGR",
                "Rolling_3Y_Volatility", "Closing NAV", "XIRR"]}
            return

    def calculate_sip_metrics(self):
        if self.sip_amount is None or self.sip_dates is None:
            return
        total_invested = self.sip_amount * len(self.sip_dates)
        years = len(self.sip_dates) / 12

        sip_nav = self.nav_series.to_frame("NAV")
        sip_allocator = SIPAllocator(sip_nav, self.sip_dates, self.sip_amount)
        units_df = sip_allocator.allocate_units()
        sip_portfolio_value = (units_df * sip_nav).sum(axis=1)

        cashflows = [{'date': d, 'value': -self.sip_amount} for d in self.sip_dates]
        cashflows.append({'date': sip_portfolio_value.index[-1], 'value': sip_portfolio_value.iloc[-1]})

        xirr = XIRRCalculator(cashflows).compute()
        self.metrics.update({
            "Total Invested": total_invested,
            "Final Assest Value": sip_portfolio_value.iloc[-1].item(),
            "Investment Horizon": years,
            "XIRR": xirr
        })

    def calculate_cagr(self):
        start_nav = self.nav_series.iloc[0]
        end_nav = self.nav_series.iloc[-1]
        num_years = len(self.nav_series) / 252
        self.metrics["CAGR"] = (end_nav / start_nav) ** (1 / num_years) - 1 if num_years > 0 else np.nan

    def calculate_sharpe_ratio(self, risk_free_rate=0.007):
        returns = self.nav_series.pct_change().dropna()
        annual_return = returns.mean() * 252
        annual_volatility = returns.std() * np.sqrt(252)
        self.metrics["Sharpe"] = (annual_return - risk_free_rate) / annual_volatility if annual_volatility > 0 else np.nan

    def calculate_sortino_ratio(self, risk_free_rate=0.007):
        returns = self.nav_series.pct_change().dropna()
        annual_return = returns.mean() * 252
        downside = returns[returns < 0]
        downside_deviation = downside.std() * np.sqrt(252)
        self.metrics["Sortino"] = (annual_return - risk_free_rate) / downside_deviation if downside_deviation > 0 else np.nan

    def calculate_rolling_metrics(self, window_days=252):
        rolling_years = [1, 3, 5, 7, 10]
        for y in rolling_years:
            window = y * window_days
            if len(self.nav_series) >= window:
                rolling = self.nav_series.rolling(window)
                roll_cagr = (rolling.apply(lambda x: x.iloc[-1] / x.iloc[0]) ** (1/y)) - 1
                self.metrics[f"Rolling_{y}Y_Mean_CAGR"] = roll_cagr.mean()
                self.metrics[f"Rolling_{y}Y_Median_CAGR"] = roll_cagr.median()

        returns = self.nav_series.pct_change().dropna()
        self.metrics["Rolling_1Y_Volatility"] = returns.rolling(1 * window_days).std().iloc[-1] * np.sqrt(252)

    def calculate_drawdowns(self):
        prices = self.nav_series
        drawdown = prices / prices.cummax() - 1
        starts, ends = [], []
        for i in range(1, len(drawdown)):
            if drawdown.iloc[i] == 0 and drawdown.iloc[i - 1] < 0:
                ends.append(i - 1)
            if drawdown.iloc[i] < 0 and drawdown.iloc[i - 1] == 0:
                starts.append(i)
        if len(starts) > len(ends):
            ends.append(len(drawdown) - 1)
        dd_info = [(s, drawdown[s:e+1].idxmin(), e, drawdown[s:e+1].min()) for s, e in zip(starts, ends)]
        dd_df = pd.DataFrame(dd_info, columns=['Start', 'Valley', 'End', 'Drawdown'])
        self.metrics["MDD"] = dd_df['Drawdown'].min() if not dd_df.empty else np.nan
        self.metrics["Avg_Top_5_DD"] = dd_df.nsmallest(5, 'Drawdown')['Drawdown'].mean() if not dd_df.empty else np.nan
        self.metrics["Avg_Top_10_DD"] = dd_df.nsmallest(10, 'Drawdown')['Drawdown'].mean() if not dd_df.empty else np.nan

    def generate_metrics(self):
        self.calculate_cagr()
        self.calculate_drawdowns()
        self.calculate_sharpe_ratio()
        self.calculate_sortino_ratio()
        self.calculate_rolling_metrics()
        self.calculate_sip_metrics()
        return self.metrics


def validate_quarter_end_dates(dates, df):
    """
    Vectorized version to adjust each quarter-end date to the last available date <= it in df.index.

    Parameters:
    - dates (Iterable): Desired quarter-end dates.
    - df (pd.DataFrame or pd.Series): DataFrame or Series with a datetime index.

    Returns:
    - List[pd.Timestamp]: Valid adjusted rebalance dates from df.index.
    """
    index = df.index if hasattr(df, 'index') else pd.to_datetime(df)
    index = pd.to_datetime(index)
    dates = pd.to_datetime(dates)
    pos = np.searchsorted(index, dates, side='right') - 1
    pos = np.clip(pos, 0, len(index) - 1)
    # Ensure that only dates that are <= original quarter-end dates are returned
    valid = index[pos]
    valid = [d if d <= orig else index[0] for d, orig in zip(valid, dates)]
    return valid


def validate_sip_dates(sip_dates, dataFrame):
    """
    Vectorized version to adjust SIP dates to the first available index >= each input date.

    Parameters:
    - sip_dates (Iterable): List of desired SIP dates.
    - dataFrame (pd.DataFrame or pd.Series): Must have a DateTimeIndex.

    Returns:
    - List[pd.Timestamp]: Valid adjusted SIP dates from dataFrame.index.
    """
    index = dataFrame.index if hasattr(dataFrame, 'index') else pd.to_datetime(dataFrame)
    index = pd.to_datetime(index)
    sip_dates = pd.to_datetime(sip_dates)
    pos = np.searchsorted(index, sip_dates, side='left')
    pos = np.clip(pos, 0, len(index) - 1)
    adjusted = index[pos].tolist()
    # Ensure the very first date in index is included if not already
    if index[0] not in adjusted:
        adjusted.insert(0, index[0])
    return adjusted


def compute_portfolio_metrics(combo, shared_returns, annual_tracking_error=0.015, management_fee=0.025, expense_ratio=0.01, sip_amount=100000):
    portfolio_daily_returns = shared_returns[list(combo)].mean(axis=1)
    total_fee = annual_tracking_error + management_fee + expense_ratio
    quarter_end_dates = pd.date_range(start=portfolio_daily_returns.index[0], end=portfolio_daily_returns.index[-1], freq='QE')
    quarter_end_dates = validate_quarter_end_dates(quarter_end_dates, portfolio_daily_returns)
    for date in quarter_end_dates:
        portfolio_daily_returns.loc[date] -= total_fee / 4
    portfolio_nav = (1 + portfolio_daily_returns).cumprod() * 100
    sip_dates = pd.date_range(start=portfolio_nav.index[0], end=portfolio_nav.index[-1], freq='MS')
    sip_dates = validate_sip_dates(sip_dates, portfolio_nav)
    metrics = FinancialMetrics(portfolio_nav, sip_amount=sip_amount, sip_dates=sip_dates).generate_metrics()
    return {"assets": combo, **metrics}


def generate_portfolios(nav_data, num_assets=5):
    combos = list(itertools.combinations(nav_data.columns, num_assets))
    shared_returns = nav_data.pct_change().dropna()
    n_jobs = max(cpu_count() - 1, 1)
    results = Parallel(n_jobs=n_jobs, backend='loky', batch_size=100)(
        delayed(compute_portfolio_metrics)(combo, shared_returns[list(combo)].copy())
        for combo in tqdm(combos, desc='Parallel Portfolio Evaluation'))
    return pd.DataFrame(results)

def filter_top_portfolios(portfolio_df, metric_preferences, top_n=5):
    scores = []
    for _, row in portfolio_df.iterrows():
        score = sum([(row[metric] - val)**2 for metric, val in metric_preferences.items() if pd.notna(row[metric])])
        scores.append(score)
    portfolio_df["score"] = scores
    portfolio_df = portfolio_df.nsmallest(top_n, "score")
    return portfolio_df.set_index("assets")


def benchmark_comparison(nav_data, benchmarks=['Nifty 50', 'Nifty 500', 'GOLD(Ounce/INR)']):
    benchmark_results = {}
    for benchmark in benchmarks:
        if benchmark in nav_data.columns:
            nav_series = nav_data[benchmark]
            metrics = FinancialMetrics(nav_series).generate_metrics()
            benchmark_results[benchmark] = metrics
    return pd.DataFrame(benchmark_results).T



def plot_top_portfolios_vs_benchmarks(nav_data, top_portfolios, sip_amount=10000, benchmarks=None):
    if benchmarks is None:
        benchmarks = ['Nifty 50', 'Nifty 500', 'GOLD(Ounce/INR)']

    sip_dates = pd.date_range(start=nav_data.index[0], end=nav_data.index[-1], freq='MS')

    for combo in top_portfolios.index:
        assets = list(combo)
        plt.figure(figsize=(12, 6))

        sip_allocator = SIPAllocator(nav_data[assets], sip_dates, sip_amount)
        units_df = sip_allocator.allocate_units()
        sip_value = (units_df * nav_data[assets]).sum(axis=1)
        plt.plot(sip_value, label="SIP Portfolio", linewidth=1.8)

        for benchmark in benchmarks:
            if benchmark in nav_data.columns:
                benchmark_nav = nav_data[[benchmark]]
                benchmark_sip_allocator = SIPAllocator(benchmark_nav, sip_dates, sip_amount)
                benchmark_units_df = benchmark_sip_allocator.allocate_units()
                benchmark_sip_value = (benchmark_units_df * benchmark_nav).sum(axis=1)
                plt.plot(benchmark_sip_value, label=f"Benchmark SIP: {benchmark}", linewidth=1.8)

        plt.title(f"Funds: {', '.join(assets)}")
        plt.xlabel("Date")
        plt.ylabel("Portfolio Value (₹)")
        plt.grid(True)
        plt.legend(loc='upper left', ncol=2)
        plt.tight_layout()
        plt.show()

## Load NAV data
nav_data = pd.read_csv("./input_data/nav_data.csv", index_col=0, parse_dates=True)
print(nav_data.shape)
## Generate and evaluate all 5-asset portfolios
allportfolio_df = generate_portfolios(nav_data, num_assets=5)
allportfolio_df.to_csv("./saved_portfolios/all_combos.csv")
# allportfolio_df = pd.read_csv("./saved_portfolios/all_combos.csv", index_col=0)
print('## all portfolio combos saved ##')

# Define user metric preferences
metric_preferences = {
    # "CAGR": 0.25,
    "XIRR": 0.10,
    "Sharpe": 1.5,
    # "Sortino": 2.0,
    # "MDD": -0.2,
    # "Rolling_1Y_Volatility":0.2,
 
}

# Filter top 5 portfolios based on preferences
top_portfolios = filter_top_portfolios(allportfolio_df, metric_preferences, top_n=5)
top_portfolios
bm_portfolios = benchmark_comparison(nav_data)
# bm_portfolios
top_portfolios

# Plot and compare SIP NAV of top portfolios vs benchmarks
plot_top_portfolios_vs_benchmarks(nav_data, top_portfolios)

