import pandas as pd
from dateutil.relativedelta import relativedelta
import numpy as np
import re
import matplotlib.pyplot as plt
from matplotlib import pyplot as plt
from matplotlib.pyplot import figure
import warnings
warnings.filterwarnings("ignore")
import math
import os
from datetime import date, timedelta, datetime
import time
from tqdm import tqdm
import pyodbc
import seaborn as sns
from scipy import stats
import xlsxwriter
from matplotlib.ticker import MaxNLocator
from matplotlib.backends.backend_pdf import PdfPages
import itertools
from data import Data
d = Data()
import numpy_financial as npf
from scipy.optimize import newton, brentq
start_time = time.perf_counter()

MF_NAV_15_Schemes_Perf = pd.read_excel('MF_NAV/MF_NAV_15_Schemes_Perf.xlsx', index_col=0).dropna(how='all')
MF_NAV_15_Schemes_Perf.index = pd.to_datetime(MF_NAV_15_Schemes_Perf.index)
MFNAV_15_schemes_NAV = pd.read_excel('MF_NAV/MFNAV_15_schemes_NAV.xlsx', index_col=0).dropna(how='all')
MFNAV_15_schemes_NAV.index = pd.to_datetime(MFNAV_15_schemes_NAV.index)
# MFNAV_15_schemes_NAV.tail()

print(MF_NAV_15_Schemes_Perf.shape)
print(MFNAV_15_schemes_NAV.shape)

print(MF_NAV_15_Schemes_Perf.columns)
print(MFNAV_15_schemes_NAV.columns)
# merge the two dataframes on the index
df = MFNAV_15_schemes_NAV.merge(MF_NAV_15_Schemes_Perf, left_index=True, right_index=True, how='outer')
df.shape