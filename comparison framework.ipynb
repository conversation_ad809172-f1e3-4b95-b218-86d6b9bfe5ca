import pandas as pd
from dateutil.relativedelta import relativedelta
import numpy as np
import re
import matplotlib.pyplot as plt
from matplotlib import pyplot as plt
from matplotlib.pyplot import figure
import warnings
warnings.filterwarnings("ignore")
import math
import os
from datetime import date, timedelta, datetime
import time
from tqdm import tqdm
import pyodbc
import seaborn as sns
from scipy import stats
import xlsxwriter
from matplotlib.ticker import MaxNLocator
from matplotlib.backends.backend_pdf import PdfPages
import itertools
from data import Data
d = Data()
import numpy_financial as npf
from scipy.optimize import newton, brentq
start_time = time.perf_counter()

MF_NAV_15_Schemes_Perf = pd.read_excel('MF_NAV/MF_NAV_15_Schemes_Perf.xlsx', index_col=0).dropna(how='all')
MF_NAV_15_Schemes_Perf.index = pd.to_datetime(MF_NAV_15_Schemes_Perf.index)
MFNAV_15_schemes_NAV = pd.read_excel('MF_NAV/MFNAV_15_schemes_NAV.xlsx', index_col=0).dropna(how='all')
MFNAV_15_schemes_NAV.index = pd.to_datetime(MFNAV_15_schemes_NAV.index)
# MFNAV_15_schemes_NAV.tail()

print(MF_NAV_15_Schemes_Perf.shape)
print(MFNAV_15_schemes_NAV.shape)

print(MF_NAV_15_Schemes_Perf.columns)
print(MFNAV_15_schemes_NAV.columns)
# merge the two dataframes on the index
df = MFNAV_15_schemes_NAV.merge(MF_NAV_15_Schemes_Perf, left_index=True, right_index=True, how='outer')
df.shape

# Portfolio Comparison Framework
# This framework allows you to input 5 fund NAVs and analyze portfolio performance

class PortfolioAnalyzer:
    def __init__(self, nav_data):
        """
        Initialize the Portfolio Analyzer with NAV data
        
        Parameters:
        nav_data (DataFrame): DataFrame with dates as index and fund NAVs as columns
        """
        self.nav_data = nav_data.copy()
        self.available_funds = list(nav_data.columns)
        print(f"Available funds: {len(self.available_funds)}")
        
    def select_funds(self, fund_names=None, fund_indices=None, interactive=False):
        """
        Select 5 funds for portfolio analysis
        
        Parameters:
        fund_names (list): List of exact fund names to select
        fund_indices (list): List of indices (0-based) to select funds
        interactive (bool): If True, display available funds for manual selection
        
        Returns:
        DataFrame: NAV data for selected funds
        """
        if interactive:
            print("Available Funds:")
            for i, fund in enumerate(self.available_funds):
                print(f"{i}: {fund}")
            return None
        
        if fund_names:
            selected_funds = [fund for fund in fund_names if fund in self.available_funds]
            if len(selected_funds) != len(fund_names):
                missing = set(fund_names) - set(selected_funds)
                print(f"Warning: These funds were not found: {missing}")
        elif fund_indices:
            selected_funds = [self.available_funds[i] for i in fund_indices if 0 <= i < len(self.available_funds)]
        else:
            # Default selection - first 5 funds
            selected_funds = self.available_funds[:5]
            
        if len(selected_funds) > 5:
            selected_funds = selected_funds[:5]
            print(f"Warning: More than 5 funds selected. Using first 5: {selected_funds}")
        elif len(selected_funds) < 5:
            print(f"Warning: Only {len(selected_funds)} funds selected. Consider adding more for better diversification.")
            
        self.selected_funds = selected_funds
        self.portfolio_data = self.nav_data[selected_funds].dropna()
        
        print(f"Selected funds: {selected_funds}")
        print(f"Portfolio data shape: {self.portfolio_data.shape}")
        print(f"Date range: {self.portfolio_data.index.min()} to {self.portfolio_data.index.max()}")
        
        return self.portfolio_data

    def calculate_returns(self, weights=None, rebalance_frequency='monthly'):
        """
        Calculate portfolio returns based on selected funds
        
        Parameters:
        weights (list): Portfolio weights for each fund (must sum to 1)
        rebalance_frequency (str): 'daily', 'weekly', 'monthly', 'quarterly', 'yearly'
        
        Returns:
        DataFrame: Portfolio returns and individual fund returns
        """
        if not hasattr(self, 'portfolio_data'):
            raise ValueError("Please select funds first using select_funds()")
            
        # Calculate daily returns for each fund
        fund_returns = self.portfolio_data.pct_change().dropna()
        
        # Default equal weights if not provided
        if weights is None:
            weights = [1/len(self.selected_funds)] * len(self.selected_funds)
        elif len(weights) != len(self.selected_funds):
            raise ValueError(f"Number of weights ({len(weights)}) must match number of selected funds ({len(self.selected_funds)})")
        elif abs(sum(weights) - 1.0) > 0.001:
            raise ValueError(f"Weights must sum to 1.0, current sum: {sum(weights)}")
            
        # Calculate portfolio returns
        portfolio_returns = (fund_returns * weights).sum(axis=1)
        
        # Create results DataFrame
        results = fund_returns.copy()
        results['Portfolio'] = portfolio_returns
        
        self.returns_data = results
        self.weights = weights
        
        return results
    
    def calculate_performance_metrics(self, risk_free_rate=0.06):
        """
        Calculate comprehensive performance metrics
        
        Parameters:
        risk_free_rate (float): Annual risk-free rate for Sharpe ratio calculation
        
        Returns:
        DataFrame: Performance metrics for each fund and portfolio
        """
        if not hasattr(self, 'returns_data'):
            raise ValueError("Please calculate returns first using calculate_returns()")
            
        metrics = {}
        
        for column in self.returns_data.columns:
            returns = self.returns_data[column].dropna()
            
            # Basic metrics
            annual_return = (1 + returns.mean()) ** 252 - 1
            annual_volatility = returns.std() * np.sqrt(252)
            sharpe_ratio = (annual_return - risk_free_rate) / annual_volatility
            
            # Cumulative returns
            cumulative_returns = (1 + returns).cumprod()
            total_return = cumulative_returns.iloc[-1] - 1
            
            # Maximum drawdown
            rolling_max = cumulative_returns.expanding().max()
            drawdown = (cumulative_returns - rolling_max) / rolling_max
            max_drawdown = drawdown.min()
            
            # Downside deviation
            negative_returns = returns[returns < 0]
            downside_deviation = negative_returns.std() * np.sqrt(252)
            
            # Sortino ratio
            sortino_ratio = (annual_return - risk_free_rate) / downside_deviation if downside_deviation > 0 else np.inf
            
            # Value at Risk (95% confidence)
            var_95 = np.percentile(returns, 5)
            
            # Calmar ratio
            calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown != 0 else np.inf
            
            metrics[column] = {
                'Annual Return (%)': round(annual_return * 100, 2),
                'Annual Volatility (%)': round(annual_volatility * 100, 2),
                'Sharpe Ratio': round(sharpe_ratio, 3),
                'Sortino Ratio': round(sortino_ratio, 3),
                'Total Return (%)': round(total_return * 100, 2),
                'Max Drawdown (%)': round(max_drawdown * 100, 2),
                'Calmar Ratio': round(calmar_ratio, 3),
                'VaR 95% (%)': round(var_95 * 100, 2),
                'Downside Deviation (%)': round(downside_deviation * 100, 2)
            }
            
        self.performance_metrics = pd.DataFrame(metrics).T
        return self.performance_metrics
    
    def plot_cumulative_returns(self, figsize=(12, 5)):
        """
        Plot cumulative returns for all funds and portfolio
        """
        if not hasattr(self, 'returns_data'):
            raise ValueError("Please calculate returns first using calculate_returns()")
            
        cumulative_returns = (1 + self.returns_data).cumprod()
        
        plt.figure(figsize=figsize)
        
        # Plot individual funds
        for fund in self.selected_funds:
            plt.plot(cumulative_returns.index, cumulative_returns[fund], 
                    label=fund[:30] + '...' if len(fund) > 30 else fund, alpha=0.7)
        
        # Highlight portfolio
        plt.plot(cumulative_returns.index, cumulative_returns['Portfolio'], 
                label='Portfolio', linewidth=3, color='red')
        
        plt.title('Cumulative Returns Comparison', fontsize=16, fontweight='bold')
        plt.xlabel('Date', fontsize=12)
        plt.ylabel('Cumulative Returns', fontsize=12)
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.show()
        
        return cumulative_returns
    
    def plot_drawdown(self, figsize=(12, 5)):
        """
        Plot drawdown analysis for the portfolio
        """
        if not hasattr(self, 'returns_data'):
            raise ValueError("Please calculate returns first using calculate_returns()")
            
        cumulative_returns = (1 + self.returns_data['Portfolio']).cumprod()
        rolling_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - rolling_max) / rolling_max
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=figsize, sharex=True)
        
        # Portfolio value
        ax1.plot(cumulative_returns.index, cumulative_returns, label='Portfolio Value', color='blue')
        ax1.plot(rolling_max.index, rolling_max, label='Peak Value', color='green', alpha=0.7)
        ax1.set_ylabel('Portfolio Value')
        ax1.set_title('Portfolio Value and Drawdown Analysis')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Drawdown
        ax2.fill_between(drawdown.index, drawdown, 0, color='red', alpha=0.3, label='Drawdown')
        ax2.plot(drawdown.index, drawdown, color='red')
        ax2.set_ylabel('Drawdown (%)')
        ax2.set_xlabel('Date')
        ax2.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.1%}'.format(y)))
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
        
        return drawdown
    
    def plot_risk_return_scatter(self, figsize=(12, 5)):
        """
        Create risk-return scatter plot
        """
        if not hasattr(self, 'performance_metrics'):
            raise ValueError("Please calculate performance metrics first")
            
        plt.figure(figsize=figsize)
        
        # Plot individual funds
        for fund in self.selected_funds:
            x = self.performance_metrics.loc[fund, 'Annual Volatility (%)']
            y = self.performance_metrics.loc[fund, 'Annual Return (%)']
            plt.scatter(x, y, s=100, alpha=0.7, label=fund[:20] + '...' if len(fund) > 20 else fund)
        
        # Highlight portfolio
        port_vol = self.performance_metrics.loc['Portfolio', 'Annual Volatility (%)']
        port_ret = self.performance_metrics.loc['Portfolio', 'Annual Return (%)']
        plt.scatter(port_vol, port_ret, s=200, color='red', marker='*', 
                   label='Portfolio', edgecolors='black', linewidth=2)
        
        plt.xlabel('Annual Volatility (%)', fontsize=12)
        plt.ylabel('Annual Return (%)', fontsize=12)
        plt.title('Risk-Return Analysis', fontsize=16, fontweight='bold')
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.show()
    
    def plot_correlation_heatmap(self, figsize=(12, 5)):
        """
        Plot correlation heatmap of selected funds
        """
        if not hasattr(self, 'returns_data'):
            raise ValueError("Please calculate returns first using calculate_returns()")
            
        # Calculate correlation matrix (excluding portfolio column)
        fund_returns = self.returns_data[self.selected_funds]
        correlation_matrix = fund_returns.corr()
        
        plt.figure(figsize=figsize)
        
        # Create shortened labels for better readability
        short_labels = [fund[:15] + '...' if len(fund) > 15 else fund for fund in self.selected_funds]
        
        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0,
                   xticklabels=short_labels, yticklabels=short_labels,
                   square=True, linewidths=0.5)
        
        plt.title('Fund Correlation Matrix', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.show()
        
        return correlation_matrix
    
    def calculate_var_cvar(self, confidence_levels=[0.95, 0.99]):
        """
        Calculate Value at Risk (VaR) and Conditional Value at Risk (CVaR)
        
        Parameters:
        confidence_levels (list): Confidence levels for VaR calculation
        
        Returns:
        DataFrame: VaR and CVaR metrics
        """
        if not hasattr(self, 'returns_data'):
            raise ValueError("Please calculate returns first using calculate_returns()")
            
        risk_metrics = {}
        
        for column in self.returns_data.columns:
            returns = self.returns_data[column].dropna()
            metrics = {}
            
            for conf_level in confidence_levels:
                # VaR (Value at Risk)
                var = np.percentile(returns, (1 - conf_level) * 100)
                
                # CVaR (Conditional Value at Risk) - Expected Shortfall
                cvar = returns[returns <= var].mean()
                
                metrics[f'VaR_{int(conf_level*100)}%'] = round(var * 100, 3)
                metrics[f'CVaR_{int(conf_level*100)}%'] = round(cvar * 100, 3)
            
            risk_metrics[column] = metrics
            
        self.var_cvar_metrics = pd.DataFrame(risk_metrics).T
        return self.var_cvar_metrics
    
    def calculate_beta_alpha(self, benchmark_returns=None):
        """
        Calculate Beta and Alpha relative to a benchmark
        
        Parameters:
        benchmark_returns (Series): Benchmark returns (if None, uses equal-weighted portfolio of all funds)
        
        Returns:
        DataFrame: Beta and Alpha metrics
        """
        if not hasattr(self, 'returns_data'):
            raise ValueError("Please calculate returns first using calculate_returns()")
            
        if benchmark_returns is None:
            # Use equal-weighted portfolio of all available funds as benchmark
            all_fund_returns = self.nav_data.pct_change().dropna()
            benchmark_returns = all_fund_returns.mean(axis=1)
            benchmark_name = "Equal-Weighted Market"
        else:
            benchmark_name = "Custom Benchmark"
            
        # Align dates
        common_dates = self.returns_data.index.intersection(benchmark_returns.index)
        benchmark_aligned = benchmark_returns.loc[common_dates]
        
        beta_alpha_metrics = {}
        
        for column in self.returns_data.columns:
            fund_returns = self.returns_data[column].loc[common_dates]
            
            # Calculate Beta using linear regression
            covariance = np.cov(fund_returns, benchmark_aligned)[0, 1]
            benchmark_variance = np.var(benchmark_aligned)
            beta = covariance / benchmark_variance if benchmark_variance != 0 else 0
            
            # Calculate Alpha (Jensen's Alpha)
            fund_mean_return = fund_returns.mean() * 252  # Annualized
            benchmark_mean_return = benchmark_aligned.mean() * 252  # Annualized
            risk_free_rate = 0.06  # Assuming 6% risk-free rate
            
            alpha = fund_mean_return - (risk_free_rate + beta * (benchmark_mean_return - risk_free_rate))
            
            # R-squared (correlation coefficient squared)
            correlation = np.corrcoef(fund_returns, benchmark_aligned)[0, 1]
            r_squared = correlation ** 2
            
            beta_alpha_metrics[column] = {
                'Beta': round(beta, 3),
                'Alpha (%)': round(alpha * 100, 3),
                'R-squared': round(r_squared, 3),
                'Correlation': round(correlation, 3)
            }
            
        self.beta_alpha_metrics = pd.DataFrame(beta_alpha_metrics).T
        self.benchmark_name = benchmark_name
        return self.beta_alpha_metrics
    
    def calculate_tracking_error(self, benchmark_returns=None):
        """
        Calculate tracking error relative to benchmark
        
        Parameters:
        benchmark_returns (Series): Benchmark returns
        
        Returns:
        DataFrame: Tracking error metrics
        """
        if not hasattr(self, 'returns_data'):
            raise ValueError("Please calculate returns first using calculate_returns()")
            
        if benchmark_returns is None:
            # Use equal-weighted portfolio of all available funds as benchmark
            all_fund_returns = self.nav_data.pct_change().dropna()
            benchmark_returns = all_fund_returns.mean(axis=1)
            
        # Align dates
        common_dates = self.returns_data.index.intersection(benchmark_returns.index)
        benchmark_aligned = benchmark_returns.loc[common_dates]
        
        tracking_metrics = {}
        
        for column in self.returns_data.columns:
            fund_returns = self.returns_data[column].loc[common_dates]
            
            # Active returns (fund returns - benchmark returns)
            active_returns = fund_returns - benchmark_aligned
            
            # Tracking error (standard deviation of active returns)
            tracking_error = active_returns.std() * np.sqrt(252)  # Annualized
            
            # Information ratio (active return / tracking error)
            active_return_mean = active_returns.mean() * 252  # Annualized
            information_ratio = active_return_mean / tracking_error if tracking_error != 0 else 0
            
            tracking_metrics[column] = {
                'Tracking Error (%)': round(tracking_error * 100, 3),
                'Active Return (%)': round(active_return_mean * 100, 3),
                'Information Ratio': round(information_ratio, 3)
            }
            
        self.tracking_metrics = pd.DataFrame(tracking_metrics).T
        return self.tracking_metrics
    
    def optimize_portfolio(self, objective='sharpe', risk_free_rate=0.06, target_return=None, target_risk=None):
        """
        Optimize portfolio weights using different objectives
        
        Parameters:
        objective (str): 'sharpe', 'min_variance', 'max_return', 'target_return', 'target_risk'
        risk_free_rate (float): Risk-free rate for Sharpe ratio calculation
        target_return (float): Target return for 'target_return' objective
        target_risk (float): Target risk for 'target_risk' objective
        
        Returns:
        dict: Optimized weights and portfolio metrics
        """
        if not hasattr(self, 'portfolio_data'):
            raise ValueError("Please select funds first using select_funds()")
            
        # Calculate returns and covariance matrix
        fund_returns = self.portfolio_data.pct_change().dropna()
        mean_returns = fund_returns.mean() * 252  # Annualized
        cov_matrix = fund_returns.cov() * 252  # Annualized
        
        n_assets = len(self.selected_funds)
        
        # Objective functions
        def portfolio_stats(weights):
            portfolio_return = np.sum(mean_returns * weights)
            portfolio_risk = np.sqrt(np.dot(weights.T, np.dot(cov_matrix, weights)))
            sharpe_ratio = (portfolio_return - risk_free_rate) / portfolio_risk
            return portfolio_return, portfolio_risk, sharpe_ratio
        
        def negative_sharpe(weights):
            return -portfolio_stats(weights)[2]
        
        def portfolio_variance(weights):
            return portfolio_stats(weights)[1] ** 2
        
        def negative_return(weights):
            return -portfolio_stats(weights)[0]
        
        # Constraints
        constraints = [{'type': 'eq', 'fun': lambda x: np.sum(x) - 1}]  # Weights sum to 1
        bounds = tuple((0, 1) for _ in range(n_assets))  # Long-only portfolio
        
        # Add specific constraints based on objective
        if objective == 'target_return' and target_return is not None:
            constraints.append({'type': 'eq', 'fun': lambda x: portfolio_stats(x)[0] - target_return})
        elif objective == 'target_risk' and target_risk is not None:
            constraints.append({'type': 'eq', 'fun': lambda x: portfolio_stats(x)[1] - target_risk})
        
        # Initial guess (equal weights)
        initial_guess = np.array([1/n_assets] * n_assets)
        
        # Optimization
        from scipy.optimize import minimize
        
        if objective == 'sharpe':
            result = minimize(negative_sharpe, initial_guess, method='SLSQP', 
                            bounds=bounds, constraints=constraints)
        elif objective == 'min_variance':
            result = minimize(portfolio_variance, initial_guess, method='SLSQP', 
                            bounds=bounds, constraints=constraints)
        elif objective == 'max_return':
            result = minimize(negative_return, initial_guess, method='SLSQP', 
                            bounds=bounds, constraints=constraints)
        elif objective == 'target_return':
            result = minimize(portfolio_variance, initial_guess, method='SLSQP', 
                            bounds=bounds, constraints=constraints)
        elif objective == 'target_risk':
            result = minimize(negative_return, initial_guess, method='SLSQP', 
                            bounds=bounds, constraints=constraints)
        else:
            raise ValueError("Invalid objective. Choose from: 'sharpe', 'min_variance', 'max_return', 'target_return', 'target_risk'")
        
        if result.success:
            optimal_weights = result.x
            opt_return, opt_risk, opt_sharpe = portfolio_stats(optimal_weights)
            
            optimization_result = {
                'objective': objective,
                'weights': dict(zip(self.selected_funds, optimal_weights)),
                'portfolio_return': opt_return,
                'portfolio_risk': opt_risk,
                'sharpe_ratio': opt_sharpe,
                'success': True
            }
            
            self.optimal_weights = optimal_weights
            self.optimization_result = optimization_result
            
            return optimization_result
        else:
            return {'success': False, 'message': 'Optimization failed'}
    
    def efficient_frontier(self, num_portfolios=100):
        """
        Generate efficient frontier
        
        Parameters:
        num_portfolios (int): Number of portfolios to generate
        
        Returns:
        DataFrame: Efficient frontier data
        """
        if not hasattr(self, 'portfolio_data'):
            raise ValueError("Please select funds first using select_funds()")
            
        fund_returns = self.portfolio_data.pct_change().dropna()
        mean_returns = fund_returns.mean() * 252
        cov_matrix = fund_returns.cov() * 252
        
        # Generate target returns
        min_ret = mean_returns.min()
        max_ret = mean_returns.max()
        target_returns = np.linspace(min_ret, max_ret, num_portfolios)
        
        efficient_portfolios = []
        
        for target_ret in target_returns:
            try:
                result = self.optimize_portfolio(objective='target_return', target_return=target_ret)
                if result['success']:
                    efficient_portfolios.append({
                        'Return': result['portfolio_return'],
                        'Risk': result['portfolio_risk'],
                        'Sharpe': result['sharpe_ratio']
                    })
            except:
                continue
                
        self.efficient_frontier_data = pd.DataFrame(efficient_portfolios)
        return self.efficient_frontier_data
    
    def plot_efficient_frontier(self, figsize=(12, 5)):
        """
        Plot the efficient frontier
        """
        if not hasattr(self, 'efficient_frontier_data'):
            print("Generating efficient frontier...")
            self.efficient_frontier()
            
        plt.figure(figsize=figsize)
        
        # Plot efficient frontier
        ef_data = self.efficient_frontier_data
        plt.plot(ef_data['Risk'] * 100, ef_data['Return'] * 100, 'b-', linewidth=2, label='Efficient Frontier')
        
        # Plot individual funds
        if hasattr(self, 'performance_metrics'):
            for fund in self.selected_funds:
                risk = self.performance_metrics.loc[fund, 'Annual Volatility (%)']
                ret = self.performance_metrics.loc[fund, 'Annual Return (%)']
                plt.scatter(risk, ret, s=100, alpha=0.7, label=fund[:15] + '...' if len(fund) > 15 else fund)
        
        # Highlight optimal portfolios if available
        if hasattr(self, 'optimization_result'):
            opt_risk = self.optimization_result['portfolio_risk'] * 100
            opt_return = self.optimization_result['portfolio_return'] * 100
            plt.scatter(opt_risk, opt_return, s=200, color='red', marker='*', 
                       label=f'Optimal ({self.optimization_result["objective"]})', 
                       edgecolors='black', linewidth=2)
        
        plt.xlabel('Risk (Annual Volatility %)', fontsize=12)
        plt.ylabel('Return (Annual Return %)', fontsize=12)
        plt.title('Efficient Frontier Analysis', fontsize=16, fontweight='bold')
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.show()
    
    def backtest_portfolio(self, start_date=None, end_date=None, rebalance_frequency='quarterly', 
                          initial_capital=100000, transaction_cost=0.001):
        """
        Backtest portfolio performance over time
        
        Parameters:
        start_date (str): Start date for backtesting (YYYY-MM-DD)
        end_date (str): End date for backtesting (YYYY-MM-DD)
        rebalance_frequency (str): 'daily', 'weekly', 'monthly', 'quarterly', 'yearly'
        initial_capital (float): Initial investment amount
        transaction_cost (float): Transaction cost as percentage of trade value
        
        Returns:
        dict: Backtesting results
        """
        if not hasattr(self, 'portfolio_data'):
            raise ValueError("Please select funds first using select_funds()")
            
        # Set default dates if not provided
        if start_date is None:
            start_date = self.portfolio_data.index[0]
        if end_date is None:
            end_date = self.portfolio_data.index[-1]
            
        # Filter data for backtesting period
        backtest_data = self.portfolio_data.loc[start_date:end_date].copy()
        
        if len(backtest_data) == 0:
            raise ValueError("No data available for the specified date range")
            
        # Calculate returns
        returns = backtest_data.pct_change().dropna()
        
        # Use optimal weights if available, otherwise equal weights
        if hasattr(self, 'optimal_weights'):
            weights = self.optimal_weights
        else:
            weights = np.array([1/len(self.selected_funds)] * len(self.selected_funds))
            
        # Rebalancing dates
        rebalance_dates = self._get_rebalance_dates(returns.index, rebalance_frequency)
        
        # Initialize tracking variables
        portfolio_value = [initial_capital]
        portfolio_weights = [weights.copy()]
        transaction_costs = [0]
        dates = [returns.index[0]]
        
        current_weights = weights.copy()
        current_value = initial_capital
        
        for i, date in enumerate(returns.index):
            # Calculate daily returns
            daily_returns = returns.loc[date].values
            
            # Update portfolio value based on returns
            daily_portfolio_return = np.sum(current_weights * daily_returns)
            current_value *= (1 + daily_portfolio_return)
            
            # Update weights due to price changes (drift)
            current_weights = current_weights * (1 + daily_returns)
            current_weights = current_weights / np.sum(current_weights)  # Normalize
            
            # Check if rebalancing is needed
            cost = 0
            if date in rebalance_dates:
                # Calculate transaction costs
                weight_changes = np.abs(current_weights - weights)
                cost = np.sum(weight_changes) * current_value * transaction_cost
                current_value -= cost
                
                # Rebalance to target weights
                current_weights = weights.copy()
            
            portfolio_value.append(current_value)
            portfolio_weights.append(current_weights.copy())
            transaction_costs.append(cost)
            dates.append(date)
        
        # Create results DataFrame
        backtest_results = pd.DataFrame({
            'Date': dates[1:],  # Skip initial date
            'Portfolio_Value': portfolio_value[1:],
            'Transaction_Cost': transaction_costs[1:]
        })
        backtest_results.set_index('Date', inplace=True)
        
        # Calculate performance metrics
        total_return = (portfolio_value[-1] - initial_capital) / initial_capital
        total_costs = sum(transaction_costs)
        
        # Calculate portfolio returns for metrics
        portfolio_returns = backtest_results['Portfolio_Value'].pct_change().dropna()
        annual_return = (1 + portfolio_returns.mean()) ** 252 - 1
        annual_volatility = portfolio_returns.std() * np.sqrt(252)
        sharpe_ratio = (annual_return - 0.06) / annual_volatility  # Assuming 6% risk-free rate
        
        # Maximum drawdown
        cumulative_returns = backtest_results['Portfolio_Value'] / initial_capital
        rolling_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - rolling_max) / rolling_max
        max_drawdown = drawdown.min()
        
        backtest_summary = {
            'start_date': start_date,
            'end_date': end_date,
            'initial_capital': initial_capital,
            'final_value': portfolio_value[-1],
            'total_return': total_return,
            'annual_return': annual_return,
            'annual_volatility': annual_volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'total_transaction_costs': total_costs,
            'rebalance_frequency': rebalance_frequency,
            'num_rebalances': len(rebalance_dates)
        }
        
        self.backtest_results = backtest_results
        self.backtest_summary = backtest_summary
        
        return {'results': backtest_results, 'summary': backtest_summary}
    
    def _get_rebalance_dates(self, date_index, frequency):
        """
        Get rebalancing dates based on frequency
        """
        if frequency == 'daily':
            return date_index
        elif frequency == 'weekly':
            return date_index[date_index.weekday == 0]  # Mondays
        elif frequency == 'monthly':
            return date_index[date_index.is_month_end]
        elif frequency == 'quarterly':
            return date_index[date_index.is_quarter_end]
        elif frequency == 'yearly':
            return date_index[date_index.is_year_end]
        else:
            return date_index[date_index.is_quarter_end]  # Default to quarterly
    
    def plot_backtest_results(self, figsize=(12, 5)):
        """
        Plot comprehensive backtesting results
        """
        if not hasattr(self, 'backtest_results'):
            raise ValueError("Please run backtest first using backtest_portfolio()")
            
        fig, axes = plt.subplots(2, 2, figsize=figsize)
        
        # Portfolio value over time
        axes[0, 0].plot(self.backtest_results.index, self.backtest_results['Portfolio_Value'])
        axes[0, 0].set_title('Portfolio Value Over Time')
        axes[0, 0].set_ylabel('Portfolio Value ($)')
        axes[0, 0].grid(True, alpha=0.3)
        
        # Cumulative returns
        cumulative_returns = self.backtest_results['Portfolio_Value'] / self.backtest_summary['initial_capital']
        axes[0, 1].plot(self.backtest_results.index, cumulative_returns)
        axes[0, 1].set_title('Cumulative Returns')
        axes[0, 1].set_ylabel('Cumulative Return Multiple')
        axes[0, 1].grid(True, alpha=0.3)
        
        # Drawdown
        rolling_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - rolling_max) / rolling_max
        axes[1, 0].fill_between(self.backtest_results.index, drawdown, 0, color='red', alpha=0.3)
        axes[1, 0].plot(self.backtest_results.index, drawdown, color='red')
        axes[1, 0].set_title('Drawdown Analysis')
        axes[1, 0].set_ylabel('Drawdown')
        axes[1, 0].yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.1%}'.format(y)))
        axes[1, 0].grid(True, alpha=0.3)
        
        # Transaction costs over time
        cumulative_costs = self.backtest_results['Transaction_Cost'].cumsum()
        axes[1, 1].plot(self.backtest_results.index, cumulative_costs)
        axes[1, 1].set_title('Cumulative Transaction Costs')
        axes[1, 1].set_ylabel('Transaction Costs ($)')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
        
        # Print summary
        print("\nBacktest Summary:")
        print(f"Period: {self.backtest_summary['start_date']} to {self.backtest_summary['end_date']}")
        print(f"Initial Capital: ${self.backtest_summary['initial_capital']:,.2f}")
        print(f"Final Value: ${self.backtest_summary['final_value']:,.2f}")
        print(f"Total Return: {self.backtest_summary['total_return']*100:.2f}%")
        print(f"Annual Return: {self.backtest_summary['annual_return']*100:.2f}%")
        print(f"Annual Volatility: {self.backtest_summary['annual_volatility']*100:.2f}%")
        print(f"Sharpe Ratio: {self.backtest_summary['sharpe_ratio']:.3f}")
        print(f"Maximum Drawdown: {self.backtest_summary['max_drawdown']*100:.2f}%")
        print(f"Total Transaction Costs: ${self.backtest_summary['total_transaction_costs']:,.2f}")
        print(f"Number of Rebalances: {self.backtest_summary['num_rebalances']}")
    
    def generate_comprehensive_report(self, save_to_file=False, filename='portfolio_analysis_report.html'):
        """
        Generate a comprehensive portfolio analysis report
        
        Parameters:
        save_to_file (bool): Whether to save the report to an HTML file
        filename (str): Filename for the saved report
        
        Returns:
        str: HTML report content
        """
        if not hasattr(self, 'selected_funds'):
            raise ValueError("Please select funds first using select_funds()")
            
        # Generate all analyses if not already done
        if not hasattr(self, 'returns_data'):
            self.calculate_returns()
        if not hasattr(self, 'performance_metrics'):
            self.calculate_performance_metrics()
        if not hasattr(self, 'var_cvar_metrics'):
            self.calculate_var_cvar()
        if not hasattr(self, 'beta_alpha_metrics'):
            self.calculate_beta_alpha()
        if not hasattr(self, 'tracking_metrics'):
            self.calculate_tracking_error()
            
        # Create HTML report
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Portfolio Analysis Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                h1 {{ color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }}
                h2 {{ color: #34495e; border-bottom: 1px solid #bdc3c7; padding-bottom: 5px; }}
                table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 12px; text-align: left; }}
                th {{ background-color: #f2f2f2; font-weight: bold; }}
                .metric-positive {{ color: #27ae60; font-weight: bold; }}
                .metric-negative {{ color: #e74c3c; font-weight: bold; }}
                .summary-box {{ background-color: #ecf0f1; padding: 20px; border-radius: 5px; margin: 20px 0; }}
                .fund-list {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; }}
            </style>
        </head>
        <body>
            <h1>Portfolio Analysis Report</h1>
            <p><strong>Generated on:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            
            <div class="summary-box">
                <h2>Portfolio Summary</h2>
                <p><strong>Number of Funds:</strong> {len(self.selected_funds)}</p>
                <p><strong>Analysis Period:</strong> {self.portfolio_data.index.min().strftime('%Y-%m-%d')} to {self.portfolio_data.index.max().strftime('%Y-%m-%d')}</p>
                <p><strong>Total Observations:</strong> {len(self.portfolio_data)}</p>
            </div>
            
            <div class="fund-list">
                <h2>Selected Funds</h2>
                <ul>
        """
        
        for i, fund in enumerate(self.selected_funds, 1):
            html_content += f"<li>{i}. {fund}</li>"
            
        html_content += """
                </ul>
            </div>
            
            <h2>Performance Metrics</h2>
        """
        
        # Add performance metrics table
        html_content += self._dataframe_to_html_table(self.performance_metrics, "Performance Metrics")
        
        # Add risk metrics
        html_content += "<h2>Risk Analysis</h2>"
        html_content += self._dataframe_to_html_table(self.var_cvar_metrics, "Value at Risk (VaR) and Conditional VaR")
        
        # Add beta/alpha analysis
        html_content += "<h2>Market Risk Analysis</h2>"
        html_content += self._dataframe_to_html_table(self.beta_alpha_metrics, f"Beta and Alpha Analysis (vs {self.benchmark_name})")
        
        # Add tracking error
        html_content += "<h2>Tracking Analysis</h2>"
        html_content += self._dataframe_to_html_table(self.tracking_metrics, "Tracking Error Analysis")
        
        # Add optimization results if available
        if hasattr(self, 'optimization_result') and self.optimization_result['success']:
            html_content += "<h2>Portfolio Optimization</h2>"
            html_content += f"<div class='summary-box'>"
            html_content += f"<p><strong>Optimization Objective:</strong> {self.optimization_result['objective'].title()}</p>"
            html_content += f"<p><strong>Expected Annual Return:</strong> <span class='metric-positive'>{self.optimization_result['portfolio_return']*100:.2f}%</span></p>"
            html_content += f"<p><strong>Expected Annual Risk:</strong> {self.optimization_result['portfolio_risk']*100:.2f}%</p>"
            html_content += f"<p><strong>Sharpe Ratio:</strong> <span class='metric-positive'>{self.optimization_result['sharpe_ratio']:.3f}</span></p>"
            html_content += "<h3>Optimal Weights:</h3><ul>"
            for fund, weight in self.optimization_result['weights'].items():
                if weight > 0.001:  # Only show weights > 0.1%
                    html_content += f"<li>{fund}: {weight*100:.1f}%</li>"
            html_content += "</ul></div>"
        
        # Add backtesting results if available
        if hasattr(self, 'backtest_summary'):
            html_content += "<h2>Backtesting Results</h2>"
            html_content += f"<div class='summary-box'>"
            html_content += f"<p><strong>Backtest Period:</strong> {self.backtest_summary['start_date']} to {self.backtest_summary['end_date']}</p>"
            html_content += f"<p><strong>Initial Capital:</strong> ${self.backtest_summary['initial_capital']:,.2f}</p>"
            html_content += f"<p><strong>Final Value:</strong> ${self.backtest_summary['final_value']:,.2f}</p>"
            
            total_return_class = 'metric-positive' if self.backtest_summary['total_return'] > 0 else 'metric-negative'
            html_content += f"<p><strong>Total Return:</strong> <span class='{total_return_class}'>{self.backtest_summary['total_return']*100:.2f}%</span></p>"
            html_content += f"<p><strong>Annual Return:</strong> <span class='{total_return_class}'>{self.backtest_summary['annual_return']*100:.2f}%</span></p>"
            html_content += f"<p><strong>Annual Volatility:</strong> {self.backtest_summary['annual_volatility']*100:.2f}%</p>"
            
            sharpe_class = 'metric-positive' if self.backtest_summary['sharpe_ratio'] > 1 else 'metric-negative'
            html_content += f"<p><strong>Sharpe Ratio:</strong> <span class='{sharpe_class}'>{self.backtest_summary['sharpe_ratio']:.3f}</span></p>"
            
            drawdown_class = 'metric-negative' if self.backtest_summary['max_drawdown'] < -0.1 else 'metric-positive'
            html_content += f"<p><strong>Maximum Drawdown:</strong> <span class='{drawdown_class}'>{self.backtest_summary['max_drawdown']*100:.2f}%</span></p>"
            html_content += f"<p><strong>Transaction Costs:</strong> ${self.backtest_summary['total_transaction_costs']:,.2f}</p>"
            html_content += f"<p><strong>Rebalancing Frequency:</strong> {self.backtest_summary['rebalance_frequency'].title()}</p>"
            html_content += "</div>"
        
        # Add correlation analysis
        if hasattr(self, 'returns_data'):
            correlation_matrix = self.returns_data[self.selected_funds].corr()
            html_content += "<h2>Correlation Analysis</h2>"
            html_content += self._dataframe_to_html_table(correlation_matrix.round(3), "Fund Correlation Matrix")
        
        # Close HTML
        html_content += """
            <div class="summary-box">
                <h2>Disclaimer</h2>
                <p><em>This analysis is for informational purposes only and should not be considered as investment advice. 
                Past performance does not guarantee future results. Please consult with a qualified financial advisor 
                before making investment decisions.</em></p>
            </div>
        </body>
        </html>
        """
        
        if save_to_file:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"Report saved to {filename}")
            
        return html_content
    
    def _dataframe_to_html_table(self, df, title=""):
        """
        Convert DataFrame to HTML table
        """
        html = f"<h3>{title}</h3>" if title else ""
        html += "<table>"
        
        # Header
        html += "<tr><th></th>"
        for col in df.columns:
            html += f"<th>{col}</th>"
        html += "</tr>"
        
        # Rows
        for idx in df.index:
            html += f"<tr><td><strong>{idx}</strong></td>"
            for col in df.columns:
                value = df.loc[idx, col]
                if isinstance(value, (int, float)):
                    if col.endswith('(%)'):
                        css_class = 'metric-positive' if value > 0 else 'metric-negative'
                        html += f"<td><span class='{css_class}'>{value}</span></td>"
                    else:
                        html += f"<td>{value}</td>"
                else:
                    html += f"<td>{value}</td>"
            html += "</tr>"
        
        html += "</table>"
        return html
    
    def simulate_sip_portfolio(self, monthly_investment=100000, start_date=None, end_date=None, 
                              weights=None, investment_day=1):
        """
        Simulate SIP (Systematic Investment Plan) portfolio without rebalancing
        
        Parameters:
        monthly_investment (float): Fixed monthly investment amount
        start_date (str): Start date for SIP (YYYY-MM-DD)
        end_date (str): End date for SIP (YYYY-MM-DD)
        weights (list): Portfolio weights for fund allocation (must sum to 1)
        investment_day (int): Day of month for investment (1-28)
        
        Returns:
        dict: SIP simulation results with cash flows and portfolio values
        """
        if not hasattr(self, 'portfolio_data'):
            raise ValueError("Please select funds first using select_funds()")
            
        # Set default dates if not provided
        if start_date is None:
            start_date = self.portfolio_data.index[0]
        if end_date is None:
            end_date = self.portfolio_data.index[-1]
            
        # Convert to datetime if strings
        if isinstance(start_date, str):
            start_date = pd.to_datetime(start_date)
        if isinstance(end_date, str):
            end_date = pd.to_datetime(end_date)
            
        # Filter data for SIP period
        sip_data = self.portfolio_data.loc[start_date:end_date].copy()
        
        if len(sip_data) == 0:
            raise ValueError("No data available for the specified date range")
            
        # Use equal weights if not provided
        if weights is None:
            weights = np.array([1/len(self.selected_funds)] * len(self.selected_funds))
        else:
            weights = np.array(weights)
            if len(weights) != len(self.selected_funds):
                raise ValueError(f"Number of weights ({len(weights)}) must match number of selected funds ({len(self.selected_funds)})")
            if abs(sum(weights) - 1.0) > 0.001:
                raise ValueError(f"Weights must sum to 1.0, current sum: {sum(weights)}")
        
        # Generate SIP investment dates
        sip_dates = []
        current_date = start_date.replace(day=min(investment_day, 28))  # Ensure valid day
        
        while current_date <= end_date:
            # Find the nearest available trading day
            available_dates = sip_data.index[sip_data.index >= current_date]
            if len(available_dates) > 0:
                nearest_date = available_dates[0]
                if nearest_date <= end_date:
                    sip_dates.append(nearest_date)
            
            # Move to next month
            if current_date.month == 12:
                current_date = current_date.replace(year=current_date.year + 1, month=1)
            else:
                current_date = current_date.replace(month=current_date.month + 1)
        
        # Initialize tracking variables
        fund_units = np.zeros(len(self.selected_funds))  # Units held in each fund
        cash_flows = []  # For XIRR calculation
        portfolio_values = []  # Daily portfolio values
        investment_amounts = []  # Cumulative investments
        sip_transactions = []  # SIP transaction details
        
        total_invested = 0
        
        # Simulate SIP investments
        for date in sip_data.index:
            # Check if this is a SIP investment date
            if date in sip_dates:
                # Invest the monthly amount
                fund_investments = monthly_investment * weights
                nav_prices = sip_data.loc[date].values
                
                # Calculate units purchased for each fund
                units_purchased = fund_investments / nav_prices
                fund_units += units_purchased
                
                total_invested += monthly_investment
                
                # Record cash flow (negative for investment)
                cash_flows.append({'date': date, 'amount': -monthly_investment})
                
                # Record transaction details
                transaction_detail = {
                    'date': date,
                    'investment': monthly_investment,
                    'nav_prices': nav_prices.copy(),
                    'units_purchased': units_purchased.copy(),
                    'cumulative_units': fund_units.copy()
                }
                sip_transactions.append(transaction_detail)
            
            # Calculate current portfolio value
            current_nav_prices = sip_data.loc[date].values
            current_portfolio_value = np.sum(fund_units * current_nav_prices)
            
            portfolio_values.append({
                'date': date,
                'portfolio_value': current_portfolio_value,
                'total_invested': total_invested,
                'unrealized_gain': current_portfolio_value - total_invested,
                'fund_values': fund_units * current_nav_prices
            })
            investment_amounts.append(total_invested)
        
        # Add final cash flow for XIRR calculation (positive for final value)
        final_value = portfolio_values[-1]['portfolio_value']
        cash_flows.append({'date': sip_data.index[-1], 'amount': final_value})
        
        # Create results DataFrames
        portfolio_df = pd.DataFrame(portfolio_values)
        portfolio_df.set_index('date', inplace=True)
        
        cash_flows_df = pd.DataFrame(cash_flows)
        cash_flows_df.set_index('date', inplace=True)
        
        sip_transactions_df = pd.DataFrame(sip_transactions)
        if len(sip_transactions_df) > 0:
            sip_transactions_df.set_index('date', inplace=True)
        
        # Store results
        self.sip_results = {
            'portfolio_values': portfolio_df,
            'cash_flows': cash_flows_df,
            'transactions': sip_transactions_df,
            'final_units': fund_units,
            'total_invested': total_invested,
            'final_value': final_value,
            'absolute_return': final_value - total_invested,
            'return_percentage': ((final_value - total_invested) / total_invested) * 100,
            'monthly_investment': monthly_investment,
            'investment_period_months': len(sip_dates),
            'weights': weights,
            'start_date': start_date,
            'end_date': end_date
        }
        
        return self.sip_results
    
    def calculate_xirr(self, cash_flows_df=None, guess=0.1):
        """
        Calculate XIRR (Extended Internal Rate of Return) for SIP portfolio
        
        Parameters:
        cash_flows_df (DataFrame): DataFrame with dates and cash flows (if None, uses SIP results)
        guess (float): Initial guess for XIRR calculation
        
        Returns:
        float: XIRR as percentage
        """
        if cash_flows_df is None:
            if not hasattr(self, 'sip_results'):
                raise ValueError("Please run SIP simulation first using simulate_sip_portfolio()")
            cash_flows_df = self.sip_results['cash_flows']
        
        # Prepare data for XIRR calculation
        dates = cash_flows_df.index.tolist()
        amounts = cash_flows_df['amount'].tolist()
        
        if len(dates) < 2:
            raise ValueError("Need at least 2 cash flows to calculate XIRR")
        
        # Convert dates to days from first date for calculation
        base_date = dates[0]
        days = [(date - base_date).days for date in dates]
        
        def xirr_equation(rate):
            """
            XIRR equation: Sum of (cash_flow / (1 + rate)^(days/365)) = 0
            """
            return sum(amount / (1 + rate) ** (day / 365.0) for amount, day in zip(amounts, days))
        
        def xirr_derivative(rate):
            """
            Derivative of XIRR equation for Newton-Raphson method
            """
            return sum(-amount * (day / 365.0) / (1 + rate) ** (day / 365.0 + 1) for amount, day in zip(amounts, days))
        
        # Newton-Raphson method to find XIRR
        rate = guess
        max_iterations = 100
        tolerance = 1e-6
        
        for i in range(max_iterations):
            try:
                f_value = xirr_equation(rate)
                f_derivative = xirr_derivative(rate)
                
                if abs(f_value) < tolerance:
                    break
                    
                if abs(f_derivative) < tolerance:
                    # Try different starting point if derivative is too small
                    rate = guess + 0.1
                    continue
                    
                new_rate = rate - f_value / f_derivative
                
                if abs(new_rate - rate) < tolerance:
                    rate = new_rate
                    break
                    
                rate = new_rate
                
            except (ZeroDivisionError, OverflowError):
                # Try with scipy's brentq method as fallback
                try:
                    from scipy.optimize import brentq
                    rate = brentq(xirr_equation, -0.99, 10.0)
                    break
                except:
                    return None
        
        # Convert to percentage
        xirr_percentage = rate * 100
        
        # Store XIRR in results
        if hasattr(self, 'sip_results'):
            self.sip_results['xirr'] = xirr_percentage
        
        return xirr_percentage
    
    def calculate_sip_metrics(self):
        """
        Calculate comprehensive SIP-specific metrics
        
        Returns:
        dict: SIP performance metrics
        """
        if not hasattr(self, 'sip_results'):
            raise ValueError("Please run SIP simulation first using simulate_sip_portfolio()")
        
        sip_data = self.sip_results
        
        # Calculate XIRR
        xirr = self.calculate_xirr()
        
        # Calculate other SIP metrics
        total_invested = sip_data['total_invested']
        final_value = sip_data['final_value']
        absolute_return = sip_data['absolute_return']
        
        # Investment period in years
        start_date = sip_data['start_date']
        end_date = sip_data['end_date']
        investment_period_years = (end_date - start_date).days / 365.25
        
        # Annualized return (simple)
        if investment_period_years > 0:
            annualized_return = ((final_value / total_invested) ** (1 / investment_period_years) - 1) * 100
        else:
            annualized_return = 0
        
        # Average cost per unit for each fund
        avg_costs = []
        if len(sip_data['transactions']) > 0:
            for i, fund in enumerate(self.selected_funds):
                total_investment_in_fund = sum(
                    row['investment'] * sip_data['weights'][i] 
                    for _, row in sip_data['transactions'].iterrows()
                )
                total_units = sip_data['final_units'][i]
                avg_cost = total_investment_in_fund / total_units if total_units > 0 else 0
                avg_costs.append(avg_cost)
        
        # Current NAV prices
        current_navs = self.portfolio_data.iloc[-1].values
        
        # Fund-wise returns
        fund_wise_metrics = {}
        for i, fund in enumerate(self.selected_funds):
            if len(avg_costs) > i and avg_costs[i] > 0:
                fund_return = ((current_navs[i] - avg_costs[i]) / avg_costs[i]) * 100
            else:
                fund_return = 0
                
            fund_wise_metrics[fund] = {
                'Average_Cost': avg_costs[i] if len(avg_costs) > i else 0,
                'Current_NAV': current_navs[i],
                'Units_Held': sip_data['final_units'][i],
                'Current_Value': sip_data['final_units'][i] * current_navs[i],
                'Fund_Return_Pct': fund_return,
                'Weight_Pct': sip_data['weights'][i] * 100
            }
        
        sip_metrics = {
            'XIRR_Pct': xirr,
            'Total_Invested': total_invested,
            'Final_Value': final_value,
            'Absolute_Return': absolute_return,
            'Return_Percentage': sip_data['return_percentage'],
            'Annualized_Return_Pct': annualized_return,
            'Investment_Period_Years': investment_period_years,
            'Monthly_Investment': sip_data['monthly_investment'],
            'Number_of_Investments': sip_data['investment_period_months'],
            'Fund_Wise_Metrics': fund_wise_metrics
        }
        
        self.sip_metrics = sip_metrics
        return sip_metrics
    
    def compare_sip_portfolios(self, portfolio_combinations, monthly_investment=100000, 
                              start_date=None, end_date=None):
        """
        Compare multiple SIP portfolio combinations
        
        Parameters:
        portfolio_combinations (list): List of dictionaries with 'funds' and 'weights' keys
                                     Example: [{'name': 'Portfolio 1', 'funds': [0,1,2,3,4], 'weights': [0.2,0.2,0.2,0.2,0.2]}]
        monthly_investment (float): Monthly SIP amount
        start_date (str): Start date for SIP comparison
        end_date (str): End date for SIP comparison
        
        Returns:
        DataFrame: Comparison results with XIRR and other metrics
        """
        comparison_results = []
        
        for i, portfolio in enumerate(portfolio_combinations):
            try:
                # Get portfolio details
                portfolio_name = portfolio.get('name', f'Portfolio_{i+1}')
                fund_indices = portfolio['funds']
                weights = portfolio.get('weights', None)
                
                # Select funds for this portfolio
                self.select_funds(fund_indices=fund_indices)
                
                # Run SIP simulation
                sip_results = self.simulate_sip_portfolio(
                    monthly_investment=monthly_investment,
                    start_date=start_date,
                    end_date=end_date,
                    weights=weights
                )
                
                # Calculate metrics
                metrics = self.calculate_sip_metrics()
                
                # Compile results
                result = {
                    'Portfolio_Name': portfolio_name,
                    'XIRR_Pct': metrics['XIRR_Pct'],
                    'Total_Invested': metrics['Total_Invested'],
                    'Final_Value': metrics['Final_Value'],
                    'Absolute_Return': metrics['Absolute_Return'],
                    'Return_Percentage': metrics['Return_Percentage'],
                    'Annualized_Return_Pct': metrics['Annualized_Return_Pct'],
                    'Investment_Period_Years': metrics['Investment_Period_Years'],
                    'Number_of_Investments': metrics['Number_of_Investments'],
                    'Selected_Funds': ', '.join([fund[:20] + '...' if len(fund) > 20 else fund for fund in self.selected_funds])
                }
                
                comparison_results.append(result)
                
            except Exception as e:
                print(f"Error processing {portfolio.get('name', f'Portfolio_{i+1}')}: {str(e)}")
                continue
        
        # Create comparison DataFrame
        comparison_df = pd.DataFrame(comparison_results)
        
        if len(comparison_df) > 0:
            # Sort by XIRR (descending)
            comparison_df = comparison_df.sort_values('XIRR_Pct', ascending=False).reset_index(drop=True)
            
            # Add ranking
            comparison_df['XIRR_Rank'] = range(1, len(comparison_df) + 1)
        
        self.sip_comparison_results = comparison_df
        return comparison_df
    
    def generate_portfolio_combinations(self, num_portfolios=10, random_seed=42):
        """
        Generate random portfolio combinations for comparison
        
        Parameters:
        num_portfolios (int): Number of random portfolios to generate
        random_seed (int): Random seed for reproducibility
        
        Returns:
        list: List of portfolio combinations
        """
        np.random.seed(random_seed)
        
        total_funds = len(self.available_funds)
        combinations = []
        
        for i in range(num_portfolios):
            # Randomly select 5 funds
            selected_indices = np.random.choice(total_funds, size=5, replace=False)
            
            # Generate random weights that sum to 1
            raw_weights = np.random.random(5)
            weights = raw_weights / raw_weights.sum()
            
            combination = {
                'name': f'Random_Portfolio_{i+1}',
                'funds': selected_indices.tolist(),
                'weights': weights.tolist()
            }
            
            combinations.append(combination)
        
        return combinations
    
    def plot_sip_performance(self, figsize=(12, 5)):
        """
        Plot comprehensive SIP performance analysis
        """
        if not hasattr(self, 'sip_results'):
            raise ValueError("Please run SIP simulation first using simulate_sip_portfolio()")
        
        fig, axes = plt.subplots(2, 2, figsize=figsize)
        
        portfolio_df = self.sip_results['portfolio_values']
        
        # 1. Investment vs Portfolio Value over time
        axes[0, 0].plot(portfolio_df.index, portfolio_df['total_invested'], 
                       label='Total Invested', color='blue', linewidth=2)
        axes[0, 0].plot(portfolio_df.index, portfolio_df['portfolio_value'], 
                       label='Portfolio Value', color='green', linewidth=2)
        axes[0, 0].fill_between(portfolio_df.index, portfolio_df['total_invested'], 
                               portfolio_df['portfolio_value'], 
                               where=(portfolio_df['portfolio_value'] >= portfolio_df['total_invested']),
                               color='green', alpha=0.3, label='Gains')
        axes[0, 0].fill_between(portfolio_df.index, portfolio_df['total_invested'], 
                               portfolio_df['portfolio_value'], 
                               where=(portfolio_df['portfolio_value'] < portfolio_df['total_invested']),
                               color='red', alpha=0.3, label='Losses')
        axes[0, 0].set_title('SIP Investment vs Portfolio Value', fontweight='bold')
        axes[0, 0].set_ylabel('Amount (₹)')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. Unrealized Gains/Losses over time
        axes[0, 1].plot(portfolio_df.index, portfolio_df['unrealized_gain'], 
                       color='purple', linewidth=2)
        axes[0, 1].fill_between(portfolio_df.index, 0, portfolio_df['unrealized_gain'], 
                               where=(portfolio_df['unrealized_gain'] >= 0),
                               color='green', alpha=0.3)
        axes[0, 1].fill_between(portfolio_df.index, 0, portfolio_df['unrealized_gain'], 
                               where=(portfolio_df['unrealized_gain'] < 0),
                               color='red', alpha=0.3)
        axes[0, 1].axhline(y=0, color='black', linestyle='-', alpha=0.5)
        axes[0, 1].set_title('Unrealized Gains/Losses Over Time', fontweight='bold')
        axes[0, 1].set_ylabel('Unrealized P&L (₹)')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. Fund-wise allocation (current values)
        if hasattr(self, 'sip_metrics'):
            fund_values = [self.sip_metrics['Fund_Wise_Metrics'][fund]['Current_Value'] 
                          for fund in self.selected_funds]
            fund_labels = [fund[:15] + '...' if len(fund) > 15 else fund 
                          for fund in self.selected_funds]
            
            axes[1, 0].pie(fund_values, labels=fund_labels, autopct='%1.1f%%', startangle=90)
            axes[1, 0].set_title('Current Portfolio Allocation', fontweight='bold')
        
        # 4. Monthly SIP investments (bar chart)
        if len(self.sip_results['transactions']) > 0:
            sip_dates = self.sip_results['transactions'].index
            sip_amounts = [self.sip_results['monthly_investment']] * len(sip_dates)
            
            axes[1, 1].bar(sip_dates, sip_amounts, color='skyblue', alpha=0.7, width=20)
            axes[1, 1].set_title('Monthly SIP Investments', fontweight='bold')
            axes[1, 1].set_ylabel('Investment Amount (₹)')
            axes[1, 1].tick_params(axis='x', rotation=45)
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
        
        # Print SIP summary
        if hasattr(self, 'sip_metrics'):
            print("\n" + "="*60)
            print("SIP PORTFOLIO PERFORMANCE SUMMARY")
            print("="*60)
            metrics = self.sip_metrics
            print(f"📊 XIRR: {metrics['XIRR_Pct']:.2f}%")
            print(f"💰 Total Invested: ₹{metrics['Total_Invested']:,.2f}")
            print(f"💎 Current Value: ₹{metrics['Final_Value']:,.2f}")
            print(f"📈 Absolute Return: ₹{metrics['Absolute_Return']:,.2f}")
            print(f"📊 Return %: {metrics['Return_Percentage']:.2f}%")
            print(f"⏱️  Investment Period: {metrics['Investment_Period_Years']:.1f} years")
            print(f"🔄 Number of SIPs: {metrics['Number_of_Investments']}")
            print(f"💵 Monthly SIP: ₹{metrics['Monthly_Investment']:,.2f}")
    
    def plot_sip_comparison(self, figsize=(12, 5)):
        """
        Plot comparison of multiple SIP portfolios
        """
        if not hasattr(self, 'sip_comparison_results'):
            raise ValueError("Please run SIP comparison first using compare_sip_portfolios()")
        
        comparison_df = self.sip_comparison_results
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=figsize)
        
        # 1. XIRR Comparison (Bar Chart)
        colors = plt.cm.viridis(np.linspace(0, 1, len(comparison_df)))
        bars = ax1.bar(range(len(comparison_df)), comparison_df['XIRR_Pct'], color=colors)
        ax1.set_title('XIRR Comparison Across Portfolios', fontweight='bold')
        ax1.set_ylabel('XIRR (%)')
        ax1.set_xlabel('Portfolio Rank')
        ax1.set_xticks(range(len(comparison_df)))
        ax1.set_xticklabels([f'#{i+1}' for i in range(len(comparison_df))])
        ax1.grid(True, alpha=0.3)
        
        # Add value labels on bars
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{height:.1f}%', ha='center', va='bottom', fontsize=9)
        
        # 2. Final Value vs Total Invested (Scatter Plot)
        scatter = ax2.scatter(comparison_df['Total_Invested'], comparison_df['Final_Value'], 
                            c=comparison_df['XIRR_Pct'], cmap='viridis', s=100, alpha=0.7)
        
        # Add diagonal line (break-even)
        min_val = min(comparison_df['Total_Invested'].min(), comparison_df['Final_Value'].min())
        max_val = max(comparison_df['Total_Invested'].max(), comparison_df['Final_Value'].max())
        ax2.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.5, label='Break-even')
        
        ax2.set_title('Final Value vs Total Invested', fontweight='bold')
        ax2.set_xlabel('Total Invested (₹)')
        ax2.set_ylabel('Final Value (₹)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # Add colorbar
        cbar = plt.colorbar(scatter, ax=ax2)
        cbar.set_label('XIRR (%)')
        
        plt.tight_layout()
        plt.show()
        
        # Print top performers
        print("\n🏆 TOP 5 PERFORMING SIP PORTFOLIOS (by XIRR):")
        print("-" * 80)
        top_5 = comparison_df.head(5)
        for idx, row in top_5.iterrows():
            print(f"#{row['XIRR_Rank']} {row['Portfolio_Name']}: XIRR = {row['XIRR_Pct']:.2f}%")
            print(f"    Return: ₹{row['Absolute_Return']:,.0f} ({row['Return_Percentage']:.1f}%)")
            print(f"    Funds: {row['Selected_Funds']}")
            print()

# Initialize the analyzer
analyzer = PortfolioAnalyzer(df)


# Demo: Show available funds for selection
analyzer.select_funds(interactive=True)

# Example 1: Default selection (first 5 funds)
portfolio_data = analyzer.select_funds()
portfolio_data.head()

# Example 2: Custom selection by indices
# Select funds by their index positions (0-based)
custom_portfolio = analyzer.select_funds(fund_indices=[0, 5, 10, 15, 20])
print("\nSelected funds:")
print(analyzer.selected_funds)

# Calculate portfolio returns with equal weights
returns_data = analyzer.calculate_returns()
print("Returns data shape:", returns_data.shape)
print("\nLast 5 days of returns:")
returns_data.tail()

# Calculate comprehensive performance metrics
performance = analyzer.calculate_performance_metrics()
print("Performance Metrics:")
performance

# Example with custom weights
custom_weights = [0.3, 0.25, 0.2, 0.15, 0.1]  # Must sum to 1.0
returns_weighted = analyzer.calculate_returns(weights=None)
performance_weighted = analyzer.calculate_performance_metrics()

# print("Custom Portfolio Weights:", custom_weights)
print("\nWeighted Portfolio Performance:")
print(performance_weighted.loc['Portfolio'])

# Plot cumulative returns comparison
cumulative_data = analyzer.plot_cumulative_returns()

# Plot drawdown analysis
drawdown_data = analyzer.plot_drawdown()

# Plot risk-return scatter
analyzer.plot_risk_return_scatter()

# Plot correlation heatmap
correlation_matrix = analyzer.plot_correlation_heatmap()
print("\nCorrelation Matrix:")
correlation_matrix.round(3)

# Calculate VaR and CVaR
var_cvar = analyzer.calculate_var_cvar()
print("Value at Risk (VaR) and Conditional VaR (CVaR):")
var_cvar

# Calculate Beta and Alpha relative to market
beta_alpha = analyzer.calculate_beta_alpha()
print(f"Beta and Alpha Analysis (vs {analyzer.benchmark_name}):")
beta_alpha

# Calculate tracking error
tracking = analyzer.calculate_tracking_error()
print("Tracking Error Analysis:")
tracking

# Optimize portfolio for maximum Sharpe ratio
sharpe_optimal = analyzer.optimize_portfolio(objective='sharpe')
print("Sharpe Ratio Optimization:")
print(f"Success: {sharpe_optimal['success']}")
if sharpe_optimal['success']:
    print(f"Optimal Sharpe Ratio: {sharpe_optimal['sharpe_ratio']:.3f}")
    print(f"Expected Return: {sharpe_optimal['portfolio_return']*100:.2f}%")
    print(f"Expected Risk: {sharpe_optimal['portfolio_risk']*100:.2f}%")
    print("\nOptimal Weights:")
    for fund, weight in sharpe_optimal['weights'].items():
        if weight > 0.01:  # Only show weights > 1%
            print(f"  {fund[:30]}...: {weight*100:.1f}%")

# Optimize portfolio for minimum variance
min_var_optimal = analyzer.optimize_portfolio(objective='min_variance')
print("Minimum Variance Optimization:")
print(f"Success: {min_var_optimal['success']}")
if min_var_optimal['success']:
    print(f"Portfolio Risk: {min_var_optimal['portfolio_risk']*100:.2f}%")
    print(f"Expected Return: {min_var_optimal['portfolio_return']*100:.2f}%")
    print(f"Sharpe Ratio: {min_var_optimal['sharpe_ratio']:.3f}")
    print("\nOptimal Weights:")
    for fund, weight in min_var_optimal['weights'].items():
        if weight > 0.01:  # Only show weights > 1%
            print(f"  {fund[:30]}...: {weight*100:.1f}%")

# Plot efficient frontier
analyzer.plot_efficient_frontier()

# Backtest the optimized portfolio
# First, let's use the Sharpe-optimized weights for backtesting
backtest_result = analyzer.backtest_portfolio(
    start_date='2015-01-01',  # Adjust dates based on your data
    end_date='2023-12-31',
    rebalance_frequency='quarterly',
    initial_capital=100000,
    transaction_cost=0.001  # 0.1% transaction cost
)

print("Backtesting completed successfully!")

# Plot backtesting results
analyzer.plot_backtest_results()

# Compare different rebalancing frequencies
frequencies = ['monthly', 'quarterly', 'yearly']
comparison_results = {}

for freq in frequencies:
    try:
        result = analyzer.backtest_portfolio(
            start_date='2020-01-01',
            end_date='2023-12-31',
            rebalance_frequency=freq,
            initial_capital=100000
        )
        comparison_results[freq] = result['summary']
    except Exception as e:
        print(f"Error with {freq} rebalancing: {e}")

# Display comparison
if comparison_results:
    comparison_df = pd.DataFrame(comparison_results).T
    print("\nRebalancing Frequency Comparison:")
    print(comparison_df[['total_return', 'annual_return', 'annual_volatility', 
                        'sharpe_ratio', 'max_drawdown', 'total_transaction_costs']].round(4))

# Generate comprehensive report
print("Generating comprehensive portfolio analysis report...")

# Generate the report (this will run all analyses if not already done)
report_html = analyzer.generate_comprehensive_report(
    save_to_file=True, 
    filename='portfolio_analysis_report.html'
)

print("\n" + "="*50)
print("PORTFOLIO ANALYSIS COMPLETE!")
print("="*50)
print("\nSummary of Analysis:")
print(f"✓ Selected {len(analyzer.selected_funds)} funds for analysis")
print(f"✓ Analyzed {len(analyzer.portfolio_data)} days of data")
print(f"✓ Period: {analyzer.portfolio_data.index.min().strftime('%Y-%m-%d')} to {analyzer.portfolio_data.index.max().strftime('%Y-%m-%d')}")

if hasattr(analyzer, 'performance_metrics'):
    portfolio_return = analyzer.performance_metrics.loc['Portfolio', 'Annual Return (%)']
    portfolio_risk = analyzer.performance_metrics.loc['Portfolio', 'Annual Volatility (%)']
    portfolio_sharpe = analyzer.performance_metrics.loc['Portfolio', 'Sharpe Ratio']
    print(f"✓ Portfolio Annual Return: {portfolio_return:.2f}%")
    print(f"✓ Portfolio Annual Risk: {portfolio_risk:.2f}%")
    print(f"✓ Portfolio Sharpe Ratio: {portfolio_sharpe:.3f}")

if hasattr(analyzer, 'optimization_result') and analyzer.optimization_result['success']:
    print(f"✓ Portfolio optimization completed ({analyzer.optimization_result['objective']})")
    
if hasattr(analyzer, 'backtest_summary'):
    print(f"✓ Backtesting completed with {analyzer.backtest_summary['total_return']*100:.2f}% total return")

print("\n📊 Comprehensive HTML report saved as 'portfolio_analysis_report.html'")
print("\n🎯 Framework Features Implemented:")
print("   • Fund selection and portfolio construction")
print("   • Performance metrics calculation")
print("   • Risk analysis (VaR, CVaR, Beta, Alpha)")
print("   • Portfolio optimization (Sharpe, Min Variance)")
print("   • Backtesting with transaction costs")
print("   • Visualization and reporting")
print("\n🚀 You can now analyze any combination of 5 funds from your dataset!")

# SIP Demo 1: Single Portfolio Analysis
print("🔄 Running SIP Analysis for a Single Portfolio...")

# Select 5 funds for SIP analysis
analyzer.select_funds(fund_indices=[0, 5, 10, 15, 20])  # You can change these indices

# Run SIP simulation
sip_results = analyzer.simulate_sip_portfolio(
    monthly_investment=100000,  # ₹1 Lakh per month
    start_date='2015-01-01',    # Adjust based on your data availability
    end_date='2023-12-31',      # Adjust based on your data availability
    weights=None,               # Equal weights (20% each)
    investment_day=1            # Invest on 1st of every month
)

# Calculate SIP metrics including XIRR
sip_metrics = analyzer.calculate_sip_metrics()

print(f"\n✅ SIP Analysis Complete!")
print(f"📊 XIRR: {sip_metrics['XIRR_Pct']:.2f}%")
print(f"💰 Total Invested: ₹{sip_metrics['Total_Invested']:,.2f}")
print(f"💎 Final Value: ₹{sip_metrics['Final_Value']:,.2f}")
print(f"📈 Absolute Return: ₹{sip_metrics['Absolute_Return']:,.2f}")

# Visualize SIP performance
analyzer.plot_sip_performance()

# SIP Demo 2: Custom Weights Portfolio
print("🔄 Running SIP Analysis...")

# Custom allocation: 40%, 25%, 20%, 10%, 5%
# custom_weights = [0.4, 0.25, 0.2, 0.1, 0.05]

sip_results_weighted = analyzer.simulate_sip_portfolio(
    monthly_investment=100000,
    start_date='2020-01-01',
    end_date='2023-12-31',
    weights=None
)

sip_metrics_weighted = analyzer.calculate_sip_metrics()

print(f"\n✅ Equal Weighted SIP Analysis Complete!")
print(f"📊 XIRR: {sip_metrics_weighted['XIRR_Pct']:.2f}%")
print(f"💰 Total Invested: ₹{sip_metrics_weighted['Total_Invested']:,.2f}")
print(f"💎 Final Value: ₹{sip_metrics_weighted['Final_Value']:,.2f}")
print(f"📈 Absolute Return: ₹{sip_metrics_weighted['Absolute_Return']:,.2f}")

print("\n📋 Fund-wise Allocation:")
for fund, metrics in sip_metrics_weighted['Fund_Wise_Metrics'].items():
    print(f"  {fund[:30]}...: {metrics['Weight_Pct']:.1f}% (₹{metrics['Current_Value']:,.0f})")

# SIP Demo 3: Compare Multiple Portfolio Combinations
print("🔄 Comparing Multiple SIP Portfolio Combinations...")

# Define different portfolio combinations to compare
portfolio_combinations = [
    {
        'name': 'Large_Cap_Heavy',
        'funds': [0, 1, 2, 3, 4],  # Adjust indices based on your fund types
        'weights': [0.3, 0.3, 0.2, 0.1, 0.1]  # Heavy on first two funds
    },
    {
        'name': 'Balanced_Portfolio',
        'funds': [5, 10, 15, 20, 25],
        'weights': [0.2, 0.2, 0.2, 0.2, 0.2]  # Equal weights
    },
    {
        'name': 'Growth_Focused',
        'funds': [2, 7, 12, 17, 22],
        'weights': [0.25, 0.25, 0.25, 0.15, 0.1]  # Growth-oriented allocation
    },
    {
        'name': 'Conservative_Mix',
        'funds': [1, 6, 11, 16, 21],
        'weights': [0.4, 0.25, 0.15, 0.15, 0.05]  # Conservative allocation
    }
]

# Run comparison
comparison_results = analyzer.compare_sip_portfolios(
    portfolio_combinations=portfolio_combinations,
    monthly_investment=100000,
    start_date='2012-01-01',
    end_date='2023-12-31'
)

print("\n📊 SIP Portfolio Comparison Results:")
print(comparison_results[['Portfolio_Name', 'XIRR_Pct', 'Return_Percentage', 'Final_Value', 'XIRR_Rank']].round(2))

# Visualize SIP comparison results
analyzer.plot_sip_comparison()

# SIP Demo 4: Random Portfolio Comparison (Monte Carlo style)
print("🔄 Generating and Comparing Random SIP Portfolios...")

# Generate 20 random portfolio combinations
random_portfolios = analyzer.generate_portfolio_combinations(num_portfolios=20, random_seed=42)

# Compare all random portfolios
random_comparison = analyzer.compare_sip_portfolios(
    portfolio_combinations=random_portfolios,
    monthly_investment=100000,
    start_date='2010-01-01',
    end_date='2024-12-31'
)

print(f"\n📊 Analyzed {len(random_comparison)} Random SIP Portfolios")
print(f"🏆 Best XIRR: {random_comparison['XIRR_Pct'].max():.2f}%")
print(f"📉 Worst XIRR: {random_comparison['XIRR_Pct'].min():.2f}%")
print(f"📊 Average XIRR: {random_comparison['XIRR_Pct'].mean():.2f}%")
print(f"📈 XIRR Std Dev: {random_comparison['XIRR_Pct'].std():.2f}%")

# Show top 5 performers
print("\n🏆 Top 5 Random Portfolios:")
top_5_random = random_comparison.head(5)
for idx, row in top_5_random.iterrows():
    print(f"  #{row['XIRR_Rank']} {row['Portfolio_Name']}: XIRR = {row['XIRR_Pct']:.2f}%")

# Final Summary
print("\n" + "="*80)
print("🎯 SIP PORTFOLIO ANALYSIS FRAMEWORK - COMPLETE!")
print("="*80)
print("\n✅ Key Features Implemented:")
print("   📈 SIP Simulation with monthly fixed investments")
print("   🎯 XIRR calculation as primary performance metric")
print("   🚫 No rebalancing - natural portfolio growth")
print("   ⚖️  Custom weight allocation support")
print("   📊 Comprehensive SIP visualizations")
print("   🔄 Multi-portfolio comparison framework")
print("   🎲 Random portfolio generation for analysis")
print("\n🎯 Perfect for SIP Investment Analysis!")
print("\n💡 Usage Tips:")
print("   • Adjust monthly_investment amount as needed")
print("   • Modify start_date and end_date based on data availability")
print("   • Experiment with different fund combinations")
print("   • Use custom weights for strategic allocation")
print("   • Compare multiple strategies to find optimal SIP portfolio")


