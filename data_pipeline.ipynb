{"cells": [{"cell_type": "code", "execution_count": null, "id": "543b2f48", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from dateutil.relativedelta import relativedelta\n", "import numpy as np\n", "import re\n", "import matplotlib.pyplot as plt\n", "from matplotlib import pyplot as plt\n", "from matplotlib.pyplot import figure\n", "# import warnings\n", "# warnings.filterwarnings(\"ignore\")\n", "import math\n", "import os\n", "from datetime import date, timedelta, datetime\n", "import time\n", "from tqdm import tqdm\n", "import pyodbc\n", "import seaborn as sns\n", "from scipy import stats\n", "import xlsxwriter\n", "from matplotlib.ticker import MaxNLocator\n", "from matplotlib.backends.backend_pdf import PdfPages\n", "import itertools\n", "# from data import Data\n", "# d = Data()\n", "import numpy_financial as npf\n", "from scipy.optimize import newton\n", "# start_time = time.perf_counter()"]}, {"cell_type": "code", "execution_count": null, "id": "9f9f8585", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'pd' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[2], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m gmdp \u001b[38;5;241m=\u001b[39m \u001b[43mpd\u001b[49m\u001b[38;5;241m.\u001b[39mread_excel(\u001b[38;5;124mr\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124m*************\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mc$\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mUsers\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mAdministrator\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mPycharmProjects\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mProjects\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mglobalMacroDataPipeline\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mHistoric Macro Data2.xlsx\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m      2\u001b[0m gmdp\u001b[38;5;241m.\u001b[39mhead()\n", "\u001b[1;31mNameError\u001b[0m: name 'pd' is not defined"]}], "source": ["gmdp = pd.read_excel(r'\\\\*************\\c$\\Users\\Administrator\\PycharmProjects\\Projects\\globalMacroDataPipeline\\Historic Macro Data2.xlsx')\n", "gmdp.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "28c5e37e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Date</th>\n", "      <th>Bitcoin</th>\n", "      <th>CAC index</th>\n", "      <th>Copper</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>DII</th>\n", "      <th>DXY</th>\n", "      <th>Dax index</th>\n", "      <th>FII</th>\n", "      <th>FTSE index</th>\n", "      <th>...</th>\n", "      <th>Japan Nikkie 225</th>\n", "      <th>Nasdaq 100 index</th>\n", "      <th>S&amp;P 500</th>\n", "      <th>Shangai composite index</th>\n", "      <th>Silver Spot USD</th>\n", "      <th>South Africa JSE</th>\n", "      <th>Taiwan weighted index</th>\n", "      <th>US 10Y</th>\n", "      <th>US 2Y</th>\n", "      <th>USD/INR</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>5306</th>\n", "      <td>2025-05-16</td>\n", "      <td>103600.0</td>\n", "      <td>7886.69</td>\n", "      <td>4.5935</td>\n", "      <td>62.49</td>\n", "      <td>1369.19</td>\n", "      <td>100.840</td>\n", "      <td>23767.43</td>\n", "      <td>2385.61</td>\n", "      <td>8684.56</td>\n", "      <td>...</td>\n", "      <td>37753.72</td>\n", "      <td>21427.94</td>\n", "      <td>5958.38</td>\n", "      <td>10179.60</td>\n", "      <td>32.354</td>\n", "      <td>13200.0</td>\n", "      <td>21129.54</td>\n", "      <td>4.482</td>\n", "      <td>3.997</td>\n", "      <td>85.552</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5307</th>\n", "      <td>2025-05-17</td>\n", "      <td>103300.0</td>\n", "      <td>7886.69</td>\n", "      <td>4.5935</td>\n", "      <td>62.49</td>\n", "      <td>1369.19</td>\n", "      <td>100.840</td>\n", "      <td>23767.43</td>\n", "      <td>2385.61</td>\n", "      <td>8684.56</td>\n", "      <td>...</td>\n", "      <td>37753.72</td>\n", "      <td>21427.94</td>\n", "      <td>5958.38</td>\n", "      <td>10179.60</td>\n", "      <td>32.354</td>\n", "      <td>13200.0</td>\n", "      <td>21129.54</td>\n", "      <td>4.482</td>\n", "      <td>3.997</td>\n", "      <td>85.552</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5308</th>\n", "      <td>2025-05-18</td>\n", "      <td>104200.0</td>\n", "      <td>7886.69</td>\n", "      <td>4.5888</td>\n", "      <td>61.46</td>\n", "      <td>1369.19</td>\n", "      <td>100.700</td>\n", "      <td>23767.43</td>\n", "      <td>2385.61</td>\n", "      <td>8684.56</td>\n", "      <td>...</td>\n", "      <td>37753.72</td>\n", "      <td>21427.94</td>\n", "      <td>5958.38</td>\n", "      <td>10179.60</td>\n", "      <td>32.480</td>\n", "      <td>13200.0</td>\n", "      <td>21129.54</td>\n", "      <td>4.484</td>\n", "      <td>3.997</td>\n", "      <td>85.552</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5309</th>\n", "      <td>2025-05-19</td>\n", "      <td>105160.0</td>\n", "      <td>7883.63</td>\n", "      <td>4.6610</td>\n", "      <td>62.14</td>\n", "      <td>1369.19</td>\n", "      <td>100.220</td>\n", "      <td>23934.98</td>\n", "      <td>2385.61</td>\n", "      <td>8699.31</td>\n", "      <td>...</td>\n", "      <td>37498.63</td>\n", "      <td>21447.05</td>\n", "      <td>5963.59</td>\n", "      <td>10171.09</td>\n", "      <td>32.505</td>\n", "      <td>12964.0</td>\n", "      <td>21129.54</td>\n", "      <td>4.451</td>\n", "      <td>3.972</td>\n", "      <td>85.330</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5310</th>\n", "      <td>2025-05-20</td>\n", "      <td>106630.0</td>\n", "      <td>7942.42</td>\n", "      <td>4.6773</td>\n", "      <td>62.61</td>\n", "      <td>1369.19</td>\n", "      <td>99.875</td>\n", "      <td>24036.11</td>\n", "      <td>2385.61</td>\n", "      <td>8781.12</td>\n", "      <td>...</td>\n", "      <td>37529.49</td>\n", "      <td>21367.37</td>\n", "      <td>5940.46</td>\n", "      <td>10249.17</td>\n", "      <td>33.310</td>\n", "      <td>13000.0</td>\n", "      <td>21129.54</td>\n", "      <td>4.475</td>\n", "      <td>3.970</td>\n", "      <td>85.498</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 25 columns</p>\n", "</div>"], "text/plain": ["           Date   Bitcoin  CAC index  Copper  Crude      DII      DXY  \\\n", "5306 2025-05-16  103600.0    7886.69  4.5935  62.49  1369.19  100.840   \n", "5307 2025-05-17  103300.0    7886.69  4.5935  62.49  1369.19  100.840   \n", "5308 2025-05-18  104200.0    7886.69  4.5888  61.46  1369.19  100.700   \n", "5309 2025-05-19  105160.0    7883.63  4.6610  62.14  1369.19  100.220   \n", "5310 2025-05-20  106630.0    7942.42  4.6773  62.61  1369.19   99.875   \n", "\n", "      Dax index      FII  FTSE index  ...  Japan Nikkie 225  Nasdaq 100 index  \\\n", "5306   23767.43  2385.61     8684.56  ...          37753.72          21427.94   \n", "5307   23767.43  2385.61     8684.56  ...          37753.72          21427.94   \n", "5308   23767.43  2385.61     8684.56  ...          37753.72          21427.94   \n", "5309   23934.98  2385.61     8699.31  ...          37498.63          21447.05   \n", "5310   24036.11  2385.61     8781.12  ...          37529.49          21367.37   \n", "\n", "      S&P 500  Shangai composite index  Silver Spot USD  South Africa JSE  \\\n", "5306  5958.38                 10179.60           32.354           13200.0   \n", "5307  5958.38                 10179.60           32.354           13200.0   \n", "5308  5958.38                 10179.60           32.480           13200.0   \n", "5309  5963.59                 10171.09           32.505           12964.0   \n", "5310  5940.46                 10249.17           33.310           13000.0   \n", "\n", "      Taiwan weighted index  US 10Y  US 2Y  USD/INR  \n", "5306               21129.54   4.482  3.997   85.552  \n", "5307               21129.54   4.482  3.997   85.552  \n", "5308               21129.54   4.484  3.997   85.552  \n", "5309               21129.54   4.451  3.972   85.330  \n", "5310               21129.54   4.475  3.970   85.498  \n", "\n", "[5 rows x 25 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["gmdp.tail()"]}, {"cell_type": "code", "execution_count": null, "id": "2b9baa8f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}